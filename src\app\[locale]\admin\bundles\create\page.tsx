import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import CreateBundleContent from '@/components/admin/create-bundle-content'

interface CreateBundlePageProps {
  params: Promise<{ locale: string }>;
}

export default async function CreateBundlePage({ params }: CreateBundlePageProps) {
  const { locale } = await params;
  const supabase = await createClient()

  // Check if user is authenticated and is admin
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect(`/${locale}/login`)
  }

  // Check admin status
  const { data: profile } = await supabase
    .from('users')
    .select('is_admin')
    .eq('id', user.id)
    .single()

  if (!profile?.is_admin) {
    redirect(`/${locale}`)
  }

  return <CreateBundleContent locale={locale} />
}
