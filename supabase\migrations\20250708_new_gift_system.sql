-- Migration to create new gift system with level and league rewards
-- This migration replaces the old gift_thresholds system with a new flexible system

-- Drop old gift_thresholds table and related data (if exists)
DROP TABLE IF EXISTS user_gifts CASCADE;
DROP TABLE IF EXISTS gift_thresholds CASCADE;

-- <PERSON><PERSON> enum types first
CREATE TYPE gift_reward_type AS ENUM ('level', 'league');
CREATE TYPE gift_claim_status AS ENUM ('pending', 'selected', 'fulfilled');

-- Create new gift_rewards table for level and league rewards
CREATE TABLE gift_rewards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type gift_reward_type NOT NULL, -- 'level' or 'league'
    trigger_value INTEGER NOT NULL, -- level number or league number
    option_1_product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    option_2_product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    option_3_product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(type, trigger_value) -- Prevent duplicate rewards for same level/league
);

-- Create user_gift_claims table to track user gift claims
CREATE TABLE user_gift_claims (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    gift_reward_id UUID NOT NULL REFERENCES gift_rewards(id) ON DELETE RESTRICT,
    selected_product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    claimed_at TIMESTAMPTZ DEFAULT NOW(),
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL, -- Optional: link to order that triggered the gift
    notes TEXT,
    status gift_claim_status DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, gift_reward_id) -- Prevent duplicate claims for same reward
);



-- Add indexes for better performance
CREATE INDEX idx_gift_rewards_type_trigger ON gift_rewards(type, trigger_value);
CREATE INDEX idx_gift_rewards_active ON gift_rewards(is_active) WHERE is_active = true;
CREATE INDEX idx_user_gift_claims_user_id ON user_gift_claims(user_id);
CREATE INDEX idx_user_gift_claims_status ON user_gift_claims(status);
CREATE INDEX idx_user_gift_claims_claimed_at ON user_gift_claims(claimed_at DESC);

-- Add updated_at triggers
CREATE TRIGGER update_gift_rewards_updated_at 
    BEFORE UPDATE ON gift_rewards 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_gift_claims_updated_at 
    BEFORE UPDATE ON user_gift_claims 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on new tables
ALTER TABLE gift_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_gift_claims ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for gift_rewards
-- Admins can manage all gift rewards
CREATE POLICY "Admins can manage gift rewards" ON gift_rewards
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );

-- Users can view active gift rewards
CREATE POLICY "Users can view active gift rewards" ON gift_rewards
    FOR SELECT USING (is_active = true);

-- Create RLS policies for user_gift_claims
-- Users can view their own gift claims
CREATE POLICY "Users can view own gift claims" ON user_gift_claims
    FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own pending gift claims (to select product)
CREATE POLICY "Users can update own pending claims" ON user_gift_claims
    FOR UPDATE USING (
        auth.uid() = user_id 
        AND status = 'pending'
    );

-- Admins can view and manage all gift claims
CREATE POLICY "Admins can manage all gift claims" ON user_gift_claims
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );

-- Insert default level rewards (levels 1-50)
INSERT INTO gift_rewards (type, trigger_value, is_active) 
SELECT 'level', generate_series(1, 50), true;

-- Insert default league rewards (leagues 1-5)
INSERT INTO gift_rewards (type, trigger_value, is_active) 
SELECT 'league', generate_series(1, 5), true;

-- Add comment explaining the new system
COMMENT ON TABLE gift_rewards IS 'New gift system: level rewards (every level) and league rewards (every 10 levels). Users choose from 3 product options.';
COMMENT ON TABLE user_gift_claims IS 'Tracks user gift claims. Status: pending (not selected), selected (product chosen), fulfilled (gift sent).';
COMMENT ON COLUMN gift_rewards.type IS 'Type of reward: level (every level) or league (every 10 levels)';
COMMENT ON COLUMN gift_rewards.trigger_value IS 'Level number (1-50) or league number (1-5) that triggers this reward';
COMMENT ON COLUMN user_gift_claims.status IS 'Claim status: pending -> selected -> fulfilled';
