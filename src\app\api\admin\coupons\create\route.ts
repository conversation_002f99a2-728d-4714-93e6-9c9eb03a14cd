import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { 
      code, 
      type, 
      value, 
      minimumOrderAmount, 
      usageLimit, 
      validUntil, 
      isActive 
    } = await request.json()
    
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Validate input
    if (!code || !type || !value || !validUntil) {
      return NextResponse.json(
        { error: 'Alle Pflichtfelder müssen ausgefüllt werden' },
        { status: 400 }
      )
    }

    if (type === 'percentage' && (value < 0 || value > 100)) {
      return NextResponse.json(
        { error: 'Prozentualer Rabatt muss zwischen 0 und 100 liegen' },
        { status: 400 }
      )
    }

    if (type === 'fixed_amount' && value < 0) {
      return NextResponse.json(
        { error: 'Fester Rabatt muss positiv sein' },
        { status: 400 }
      )
    }

    // Check if code already exists
    const { data: existingCoupon } = await supabase
      .from('coupons')
      .select('id')
      .eq('code', code.toUpperCase())
      .single()

    if (existingCoupon) {
      return NextResponse.json(
        { error: 'Ein Gutschein mit diesem Code existiert bereits' },
        { status: 400 }
      )
    }

    // Create coupon
    const { data, error } = await supabase
      .from('coupons')
      .insert({
        code: code.toUpperCase(),
        type,
        value,
        minimum_order_amount: minimumOrderAmount || null,
        usage_limit: usageLimit || null,
        valid_until: validUntil,
        is_active: isActive,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating coupon:', error)
      return NextResponse.json(
        { error: 'Fehler beim Erstellen des Gutscheins' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true, coupon: data })
  } catch (error) {
    console.error('Error in coupon create:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}
