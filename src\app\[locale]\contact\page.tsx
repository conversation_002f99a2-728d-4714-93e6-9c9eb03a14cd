'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Mail, Phone, MapPin, Clock, Send } from 'lucide-react';
import { toast } from 'sonner';

export default function ContactPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const t = useTranslations('contact');

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast.success(t('form.success'));
    setIsSubmitting(false);
    
    // Reset form
    (e.target as HTMLFormElement).reset();
  };

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl lg:text-5xl font-bold mb-6">
          <span className="text-primary">{t('title')}</span>
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          {t('subtitle')}
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-12">
        {/* Contact Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Send className="mr-2 h-5 w-5" />
              {t('form.submit')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">{t('form.firstName')} *</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    required
                    placeholder={t('form.firstNamePlaceholder')}
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">{t('form.lastName')} *</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    required
                    placeholder={t('form.lastNamePlaceholder')}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email">{t('form.email')} *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  required
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <Label htmlFor="phone">{t('form.phone')}</Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  placeholder="+41 79 342 65 74"
                />
              </div>

              <div>
                <Label htmlFor="subject">{t('form.subject')} *</Label>
                <Select name="subject" required>
                  <SelectTrigger>
                    <SelectValue placeholder={t('form.subjectPlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">{t('form.subjects.general')}</SelectItem>
                    <SelectItem value="order">{t('form.subjects.order')}</SelectItem>
                    <SelectItem value="product">{t('form.subjects.product')}</SelectItem>
                    <SelectItem value="technical">{t('form.subjects.technical')}</SelectItem>
                    <SelectItem value="partnership">{t('form.subjects.partnership')}</SelectItem>
                    <SelectItem value="complaint">{t('form.subjects.complaint')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="message">{t('form.message')} *</Label>
                <Textarea
                  id="message"
                  name="message"
                  required
                  placeholder={t('form.messagePlaceholder')}
                  rows={6}
                />
              </div>

              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? '...' : t('form.submit')}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="mr-2 h-5 w-5" />
                {t('info.email')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg font-semibold"><EMAIL></p>
              <p className="text-muted-foreground">
                {t('info.emailResponse')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Phone className="mr-2 h-5 w-5" />
                {t('info.phone')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg font-semibold">+41 79 342 65 74</p>
              <p className="text-muted-foreground">
                {t('info.phoneHours')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="mr-2 h-5 w-5" />
                {t('info.address')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <p className="font-semibold">PrimeCaffe AG</p>
                <p>Bahnhofstrasse 123</p>
                <p>8001 Zürich</p>
                <p>Schweiz</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="mr-2 h-5 w-5" />
                {t('info.hours')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>{t('schedule.mondayFriday')}</span>
                  <span>9:00 - 17:00</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('schedule.saturday')}</span>
                  <span>10:00 - 14:00</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('schedule.sunday')}</span>
                  <span>{t('schedule.closed')}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="mt-16">
        <Card>
          <CardHeader>
            <CardTitle>{t('quickFaq.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">{t('quickFaq.deliveryTime.question')}</h3>
                <p className="text-muted-foreground text-sm">
                  {t('quickFaq.deliveryTime.answer')}
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">{t('quickFaq.cancelOrder.question')}</h3>
                <p className="text-muted-foreground text-sm">
                  {t('quickFaq.cancelOrder.answer')}
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">{t('quickFaq.bulkDiscount.question')}</h3>
                <p className="text-muted-foreground text-sm">
                  {t('quickFaq.bulkDiscount.answer')}
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">{t('quickFaq.freshRoasted.question')}</h3>
                <p className="text-muted-foreground text-sm">
                  {t('quickFaq.freshRoasted.answer')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
