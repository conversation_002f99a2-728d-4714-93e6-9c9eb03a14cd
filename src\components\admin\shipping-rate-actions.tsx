'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { MoreHorizontal, Edit, Trash2, ToggleLeft, ToggleRight } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'

interface ShippingRate {
  id: string
  country: string
  cost: number
  free_shipping_threshold?: number
  estimated_days: string
  is_active: boolean
}

interface ShippingRateActionsProps {
  rate: ShippingRate
}

export function ShippingRateActions({ rate }: ShippingRateActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const t = useTranslations('admin.bundleManagement')

  const handleToggleActive = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/shipping/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: rate.id,
          is_active: !rate.is_active,
        }),
      })

      if (response.ok) {
        toast({
          title: t('successToggle'),
          description: `${t('successToggle')} ${!rate.is_active ? t('activated') : t('deactivated')}.`,
        })
        router.refresh()
      } else {
        throw new Error(t('errorToggle'))
      }
    } catch {
      toast({
        title: t('errorToggle'),
        description: t('errorToggle'),
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/shipping/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: rate.id }),
      })

      if (response.ok) {
        toast({
          title: t('successDelete'),
          description: t('successDelete'),
        })
        router.refresh()
      } else {
        throw new Error(t('errorDelete'))
      }
    } catch {
      toast({
        title: t('errorDelete'),
        description: t('errorDelete'),
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
      setShowDeleteDialog(false)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t('openMenu')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={() => router.push(`/admin/shipping/edit/${rate.id}`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            {t('edit')}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleToggleActive} disabled={loading}>
            {rate.is_active ? (
              <>
                <ToggleLeft className="mr-2 h-4 w-4" />
                {t('deactivate')}
              </>
            ) : (
              <>
                <ToggleRight className="mr-2 h-4 w-4" />
                {t('activate')}
              </>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {t('delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('deleteConfirm')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('deleteMessage')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={loading}
            >
              {loading ? t('deleting') : t('delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
