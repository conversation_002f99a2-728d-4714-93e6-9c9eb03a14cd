'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { MapPin, MoreVertical, Edit, Trash2, Star } from 'lucide-react';
import type { Database } from '@/lib/supabase/database.types';

type UserAddress = Database['public']['Tables']['user_addresses']['Row'];

interface AddressListProps {
  addresses: UserAddress[];
  onEdit: (address: UserAddress) => void;
  onDelete: (id: string) => void;
  onSetDefault: (id: string, type: 'billing' | 'shipping') => void;
  loading?: boolean;
}

export default function AddressList({ 
  addresses, 
  onEdit, 
  onDelete, 
  onSetDefault, 
  loading = false 
}: AddressListProps) {
  const t = useTranslations('account.addresses');
  const tMessages = useTranslations('account.addresses.messages');
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDelete = async (id: string) => {
    setDeletingId(id);
    try {
      await onDelete(id);
    } finally {
      setDeletingId(null);
    }
  };

  const handleSetDefault = async (id: string, type: 'billing' | 'shipping') => {
    try {
      await onSetDefault(id, type);
    } catch (error) {
      console.error('Error setting default address:', error);
    }
  };

  if (addresses.length === 0) {
    return (
      <div className="text-center py-12">
        <MapPin className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">{t('emptyTitle')}</h3>
        <p className="text-muted-foreground mb-4">
          {t('emptyDescription')}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {addresses.map((address) => (
        <Card key={address.id} className="relative">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant={address.type === 'billing' ? 'default' : 'secondary'}>
                    {address.type === 'billing' ? t('form.typeBilling') : t('form.typeShipping')}
                  </Badge>
                  {address.is_default && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-current" />
                      {t('default')}
                    </Badge>
                  )}
                </div>
                <div className="space-y-1">
                  <p className="font-medium">
                    {address.first_name} {address.last_name}
                  </p>
                  {address.company && (
                    <p className="text-sm text-muted-foreground">{address.company}</p>
                  )}
                  <p className="text-sm">{address.street_address}</p>
                  <p className="text-sm">
                    {address.postal_code} {address.city}
                  </p>
                  <p className="text-sm text-muted-foreground">{address.country}</p>
                </div>
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    disabled={loading || deletingId === address.id}
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(address)}>
                    <Edit className="h-4 w-4 mr-2" />
                    {t('edit')}
                  </DropdownMenuItem>
                  {!address.is_default && (
                    <DropdownMenuItem 
                      onClick={() => handleSetDefault(address.id, address.type)}
                    >
                      <Star className="h-4 w-4 mr-2" />
                      {t('setDefault')}
                    </DropdownMenuItem>
                  )}
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <DropdownMenuItem 
                        onSelect={(e) => e.preventDefault()}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        {t('delete')}
                      </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>{t('delete')}</AlertDialogTitle>
                        <AlertDialogDescription>
                          {tMessages('confirmDelete')}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>{t('form.cancel')}</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(address.id)}
                          className="bg-red-600 hover:bg-red-700"
                          disabled={deletingId === address.id}
                        >
                          {deletingId === address.id ? 'Eliminazione...' : t('delete')}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
