-- Add user-friendly order numbers to orders table
-- Migration: 20250626_add_order_numbers.sql

-- Add order_number column to orders table
ALTER TABLE orders ADD COLUMN order_number VARCHAR(10) UNIQUE;

-- Create sequence for order numbers
CREATE SEQUENCE IF NOT EXISTS order_number_seq START 1;

-- Function to generate formatted order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    current_year TEXT;
    next_number INTEGER;
    order_number TEXT;
BEGIN
    -- Get current year (last 2 digits)
    current_year := RIGHT(EXTRACT(YEAR FROM NOW())::TEXT, 2);
    
    -- Get next sequence number
    next_number := nextval('order_number_seq');
    
    -- Format as PC + year + 4-digit number
    order_number := 'PC' || current_year || LPAD(next_number::TEXT, 4, '0');
    
    RETURN order_number;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to automatically generate order number on insert
CREATE OR <PERSON>EPLACE FUNCTION set_order_number()
<PERSON><PERSON>URNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically set order number on insert
CREATE TRIGGER set_order_number_trigger
    BEFORE INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION set_order_number();

-- Generate order numbers for existing orders
-- This will assign friendly numbers to existing orders based on creation date
DO $$
DECLARE
    order_record RECORD;
    year_suffix TEXT;
    counter INTEGER := 1;
    current_year_processing TEXT := '';
BEGIN
    -- Process existing orders in chronological order
    FOR order_record IN 
        SELECT id, created_at 
        FROM orders 
        WHERE order_number IS NULL 
        ORDER BY created_at ASC
    LOOP
        -- Get year suffix for this order
        year_suffix := RIGHT(EXTRACT(YEAR FROM order_record.created_at)::TEXT, 2);
        
        -- Reset counter if we're in a new year
        IF current_year_processing != year_suffix THEN
            current_year_processing := year_suffix;
            counter := 1;
        END IF;
        
        -- Update the order with the generated number
        UPDATE orders 
        SET order_number = 'PC' || year_suffix || LPAD(counter::TEXT, 4, '0')
        WHERE id = order_record.id;
        
        counter := counter + 1;
    END LOOP;
    
    -- Update the sequence to continue from where we left off for current year
    IF current_year_processing = RIGHT(EXTRACT(YEAR FROM NOW())::TEXT, 2) THEN
        PERFORM setval('order_number_seq', counter);
    END IF;
END $$;

-- Create index on order_number for faster lookups
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);

-- Add comment to document the new field
COMMENT ON COLUMN orders.order_number IS 'Human-readable order number in format PC24XXXX (PC + year + sequential number)';
