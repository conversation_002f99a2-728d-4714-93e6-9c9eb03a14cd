'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useTranslations } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { FileText, Settings, Plus, Eye } from 'lucide-react'

interface ProductTemplate {
  id: string
  name: string
  description: string
  title_template: string
  category: 'coffee' | 'accessories'
  coffee_type?: string
  brand?: string
  blend?: string
  machine_compatibility?: string[]
  pack_quantity?: number
  pack_weight_grams?: number
  price?: number
  discount_price?: number
  cost_per_espresso?: number
  inventory_count?: number
  purchase_cost?: number
  is_available?: boolean
  is_default: boolean
}

interface ProductTemplateSelectorProps {
  selectedTemplate?: ProductTemplate | null
  onTemplateSelect: (template: ProductTemplate | null) => void
  onTemplateApply: (template: ProductTemplate) => void
  category?: 'coffee' | 'accessories'
  showPreview?: boolean
  previewData?: Record<string, any> // eslint-disable-line @typescript-eslint/no-explicit-any
}

export default function ProductTemplateSelector({
  selectedTemplate,
  onTemplateSelect,
  onTemplateApply,
  category,
  showPreview = true,
  previewData = {}
}: ProductTemplateSelectorProps) {
  const t = useTranslations('admin.products')
  const tCommon = useTranslations('admin.common')
  const { toast } = useToast()
  const supabase = useMemo(() => createClient(), [])

  const [templates, setTemplates] = useState<ProductTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    title_template: '',
    category: category || 'coffee' as 'coffee' | 'accessories'
  })

  const loadTemplates = useCallback(async () => {
    try {
      setLoading(true)
      let query = supabase
        .from('product_templates')
        .select('*')
        .order('is_default', { ascending: false })
        .order('name')

      if (category) {
        query = query.eq('category', category)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error loading templates:', error)
        toast({
          title: t('templates.loadError'),
          description: error.message,
          variant: 'destructive'
        })
        return
      }

      setTemplates(data || [])
    } catch (error) {
      console.error('Error loading templates:', error)
    } finally {
      setLoading(false)
    }
  }, [category, supabase, toast, t])

  useEffect(() => {
    loadTemplates()
  }, [loadTemplates])

  const createTemplate = async () => {
    try {
      if (!newTemplate.name || !newTemplate.title_template) {
        toast({
          title: t('templates.createError'),
          description: t('templates.fillRequired'),
          variant: 'destructive'
        })
        return
      }

      const { data, error } = await supabase
        .from('product_templates')
        .insert([newTemplate])
        .select()
        .single()

      if (error) {
        console.error('Error creating template:', error)
        toast({
          title: t('templates.createError'),
          description: error.message,
          variant: 'destructive'
        })
        return
      }

      toast({
        title: t('templates.createSuccess'),
        description: t('templates.templateCreated')
      })

      setTemplates([...templates, data])
      setShowCreateForm(false)
      setNewTemplate({
        name: '',
        description: '',
        title_template: '',
        category: category || 'coffee'
      })
    } catch (error) {
      console.error('Error creating template:', error)
    }
  }

  const generatePreview = (template: string, data: Record<string, any>) => { // eslint-disable-line @typescript-eslint/no-explicit-any
    let preview = template

    // Replace placeholders with preview data (localized)
    const replacements = {
      '{brand}': data.brand || 'Lavazza',
      '{coffee_type}': data.coffee_type === 'capsules' ? t('admin.products.productForm.coffeeTypes.capsules') :
                      data.coffee_type === 'pods' ? t('admin.products.productForm.coffeeTypes.pods') :
                      data.coffee_type === 'beans' ? t('admin.products.productForm.coffeeTypes.beans') :
                      data.coffee_type === 'ground' ? t('admin.products.productForm.coffeeTypes.ground') :
                      t('admin.products.productForm.coffeeTypes.capsules'),
      '{blend}': data.blend || 'Crema e Gusto',
      '{pack_quantity}': data.pack_quantity?.toString() || '10',
      '{pack_weight_grams}': data.pack_weight_grams?.toString() || '250',
      '{title}': data.title || 'Caffè Premium'
    }

    Object.entries(replacements).forEach(([placeholder, value]) => {
      preview = preview.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value)
    })

    // Clean up remaining placeholders
    preview = preview.replace(/\{[^}]*\}/g, '')
    preview = preview.replace(/\s+/g, ' ')
    preview = preview.replace(/\s*-\s*-\s*/g, ' - ')
    preview = preview.replace(/^\s*-\s*|\s*-\s*$/g, '')
    preview = preview.trim()

    return preview
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {t('templates.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Template Selection */}
        <div className="space-y-2">
          <Label>{t('templates.selectTemplate')}</Label>
          <div className="flex gap-2">
            <Select
              value={selectedTemplate?.id || 'no-template'}
              onValueChange={(value) => {
                if (value === 'no-template') {
                  onTemplateSelect(null)
                } else {
                  const template = templates.find(t => t.id === value) || null
                  onTemplateSelect(template)
                }
              }}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder={t('templates.selectPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="no-template">{t('templates.noTemplate')}</SelectItem>
                {templates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    <div className="flex items-center gap-2">
                      {template.name}
                      {template.is_default && (
                        <Badge variant="secondary" className="text-xs">
                          {t('templates.default')}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCreateForm(!showCreateForm)}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Template Preview */}
        {selectedTemplate && showPreview && (
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              {t('templates.preview')}
            </Label>
            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm font-medium">{t('templates.template')}:</p>
              <p className="text-sm text-muted-foreground mb-2">{selectedTemplate.title_template}</p>
              <p className="text-sm font-medium">{t('templates.result')}:</p>
              <p className="text-sm">{generatePreview(selectedTemplate.title_template, previewData)}</p>
            </div>
          </div>
        )}

        {/* Apply Template Button */}
        {selectedTemplate && (
          <Button
            onClick={() => onTemplateApply(selectedTemplate)}
            className="w-full"
          >
            <Settings className="mr-2 h-4 w-4" />
            {t('templates.applyTemplate')}
          </Button>
        )}

        {/* Create New Template Form */}
        {showCreateForm && (
          <div className="space-y-4 p-4 border rounded-lg">
            <h4 className="font-medium">{t('templates.createNew')}</h4>
            
            <div className="space-y-2">
              <Label htmlFor="template-name">{t('templates.name')}</Label>
              <Input
                id="template-name"
                value={newTemplate.name}
                onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                placeholder={t('templates.namePlaceholder')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="template-description">{t('templates.description')}</Label>
              <Textarea
                id="template-description"
                value={newTemplate.description}
                onChange={(e) => setNewTemplate({ ...newTemplate, description: e.target.value })}
                placeholder={t('templates.descriptionPlaceholder')}
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="template-pattern">{t('templates.titleTemplate')}</Label>
              <Input
                id="template-pattern"
                value={newTemplate.title_template}
                onChange={(e) => setNewTemplate({ ...newTemplate, title_template: e.target.value })}
                placeholder="{brand} {coffee_type} - {blend} ({pack_quantity} pz)"
              />
              <div className="space-y-2">
                <p className="text-xs text-muted-foreground">
                  {t('templates.availableTags')}:
                </p>
                <div className="flex flex-wrap gap-2">
                  {[
                    { tag: '{brand}', label: t('admin.products.productForm.brand') },
                    { tag: '{coffee_type}', label: t('admin.products.productForm.coffeeType') },
                    { tag: '{blend}', label: t('admin.products.productForm.blend') },
                    { tag: '{pack_quantity}', label: t('admin.products.productForm.packQuantity') },
                    { tag: '{pack_weight_grams}', label: t('admin.products.productForm.packWeight') },
                    { tag: '{title}', label: t('admin.products.productForm.title') }
                  ].map(({ tag, label }) => (
                    <button
                      key={tag}
                      type="button"
                      onClick={() => {
                        const input = document.getElementById('template-pattern') as HTMLInputElement
                        if (input) {
                          const cursorPos = input.selectionStart || 0
                          const textBefore = newTemplate.title_template.substring(0, cursorPos)
                          const textAfter = newTemplate.title_template.substring(cursorPos)
                          const newValue = textBefore + tag + textAfter
                          setNewTemplate({ ...newTemplate, title_template: newValue })

                          // Set cursor position after the inserted tag
                          setTimeout(() => {
                            input.focus()
                            input.setSelectionRange(cursorPos + tag.length, cursorPos + tag.length)
                          }, 0)
                        }
                      }}
                      className="group px-3 py-2 text-sm bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 text-blue-700 hover:text-blue-800 rounded-lg border border-blue-200 hover:border-blue-300 transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md"
                      title={`Clicca per inserire: ${tag}`}
                    >
                      <span className="font-mono text-xs text-blue-600 group-hover:text-blue-700">{tag}</span>
                      <span className="ml-2 text-xs text-gray-600 group-hover:text-gray-700">({label})</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {newTemplate.title_template && (
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm font-medium">{t('templates.preview')}:</p>
                <p className="text-sm">{generatePreview(newTemplate.title_template, previewData)}</p>
              </div>
            )}

            <div className="flex gap-2">
              <Button onClick={createTemplate} size="sm">
                {t('templates.create')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreateForm(false)}
              >
                {tCommon('cancel')}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
