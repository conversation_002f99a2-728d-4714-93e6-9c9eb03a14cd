import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 403 })
    }

    if (!data.level || !data.name) {
      return NextResponse.json({ error: 'Level und Name erforderlich' }, { status: 400 })
    }

    const { data: level, error } = await supabase
      .from('user_levels')
      .insert({
        level: data.level,
        name: data.name,
        minimum_points: data.minimum_points || 0,
        discount_percentage: data.discount_percentage || 0,
        points_multiplier: data.points_multiplier || 1,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating level:', error)
      return NextResponse.json({ error: 'Fehler beim Erstellen des Levels' }, { status: 500 })
    }

    return NextResponse.json({ success: true, level })
  } catch (error) {
    console.error('Error in level create:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}

