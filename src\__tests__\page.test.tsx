import { redirect } from 'next/navigation'
import { headers } from 'next/headers'
import Home from '../app/page'

// Mock the redirect function and headers
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}))

jest.mock('next/headers', () => ({
  headers: jest.fn(),
}))

const mockRedirect = redirect as jest.MockedFunction<typeof redirect>
const mockHeaders = headers as jest.MockedFunction<typeof headers>

describe('Home', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock headers to return empty Accept-Language
    mockHeaders.mockResolvedValue({
      get: jest.fn().mockReturnValue(''),
    } as Headers)
  })

  it('should redirect to default locale when no browser language detected', async () => {
    await Home()
    expect(mockRedirect).toHaveBeenCalledWith('/de')
  })

  it('should redirect to detected browser language', async () => {
    // Mock headers to return Italian as preferred language
    mockHeaders.mockResolvedValue({
      get: jest.fn().mockReturnValue('it-IT,it;q=0.9,en;q=0.8'),
    } as Headers)

    await Home()
    expect(mockRedirect).toHaveBeenCalledWith('/it')
  })

  it('should redirect to French when detected', async () => {
    // Mock headers to return French as preferred language
    mockHeaders.mockResolvedValue({
      get: jest.fn().mockReturnValue('fr-FR,fr;q=0.9,en;q=0.8'),
    } as Headers)

    await Home()
    expect(mockRedirect).toHaveBeenCalledWith('/fr')
  })

  it('should fallback to default locale for unsupported language', async () => {
    // Mock headers to return unsupported language
    mockHeaders.mockResolvedValue({
      get: jest.fn().mockReturnValue('es-ES,es;q=0.9,en;q=0.8'),
    } as Headers)

    await Home()
    expect(mockRedirect).toHaveBeenCalledWith('/de')
  })

  it('should be a function', () => {
    expect(typeof Home).toBe('function')
  })
})
