'use client'

import BundleForm from '@/components/admin/bundle-form'
import { useTranslations } from 'next-intl'
import { AdminBackButton } from './admin-back-button'

interface Bundle {
  id: string
  title: string
  description: string
  image: string
  total_price: number
  discount_price?: number
  is_available: boolean
  bundle_items?: Array<{
    quantity: number
    products: {
      id: string
      title: string
      price: number
      discount_price?: number
      images: string[]
    }
  }>
}

interface EditBundleContentProps {
  locale: string
  bundle: Bundle
}

export default function EditBundleContent({ locale, bundle }: EditBundleContentProps) {
  const t = useTranslations('admin.bundleManagement')

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        {/* Back to Dashboard Button */}
        <div className="mb-6">
          <AdminBackButton />
        </div>

        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('editTitle')}</h1>
          <p className="text-muted-foreground">
            {t('editSubtitle')} &quot;{bundle.title}&quot;
          </p>
        </div>

        <BundleForm locale={locale} bundle={bundle} />
      </div>
    </div>
  )
}
