import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { supabaseAdmin } from '@/lib/supabase/admin'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('search')

    if (!query || query.length < 2) {
      return NextResponse.json({ customers: [] })
    }

    // Verify admin authentication
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (profileError || !profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Search for customers by email, first_name, or last_name
    const { data: customers, error: searchError } = await supabaseAdmin
      .from('users')
      .select(`
        id,
        email,
        first_name,
        last_name,
        phone,
        user_addresses (
          id,
          type,
          first_name,
          last_name,
          company,
          street_address,
          city,
          postal_code,
          country,
          is_default
        )
      `)
      .or(`email.ilike.%${query}%,first_name.ilike.%${query}%,last_name.ilike.%${query}%`)
      .eq('is_admin', false)
      .limit(10)

    if (searchError) {
      console.error('Error searching customers:', searchError)
      return NextResponse.json({ error: 'Search failed' }, { status: 500 })
    }

    return NextResponse.json({ customers: customers || [] })

  } catch (error) {
    console.error('Error in customer search:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
