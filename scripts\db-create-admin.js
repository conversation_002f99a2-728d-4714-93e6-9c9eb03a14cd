#!/usr/bin/env node

/**
 * Create Admin User for PrimeCaffe
 * Creates an admin user for production management
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

function askPassword(question) {
  return new Promise((resolve) => {
    const stdin = process.stdin;
    const stdout = process.stdout;
    
    stdout.write(question);
    stdin.setRawMode(true);
    stdin.resume();
    stdin.setEncoding('utf8');
    
    let password = '';
    
    stdin.on('data', function(char) {
      char = char + '';
      
      switch (char) {
        case '\n':
        case '\r':
        case '\u0004':
          stdin.setRawMode(false);
          stdin.pause();
          stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          stdout.write('*');
          break;
      }
    });
  });
}

async function createAdminUser() {
  console.log('🔐 PrimeCaffe Admin User Creation\n');
  
  try {
    const email = await askQuestion('Admin Email: ');
    const password = await askPassword('Admin Password: ');
    const firstName = await askQuestion('First Name: ');
    const lastName = await askQuestion('Last Name: ');
    
    console.log('\n🔄 Creating admin user...');
    
    // Create auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    });
    
    if (authError) {
      console.error('❌ Failed to create auth user:', authError.message);
      return false;
    }
    
    console.log('✅ Auth user created');
    
    // Create user profile with admin privileges
    const { error: profileError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        first_name: firstName,
        last_name: lastName,
        is_admin: true,
        current_level: 4, // Platinum level
        total_points: 0,
        lifetime_spend: 0
      });
    
    if (profileError) {
      console.error('❌ Failed to create user profile:', profileError.message);
      return false;
    }
    
    console.log('✅ Admin profile created');
    console.log('\n🎉 Admin user created successfully!');
    console.log(`📧 Email: ${email}`);
    console.log('🔑 Admin privileges: Enabled');
    console.log('⭐ Level: Platinum (4)');
    
    return true;
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    return false;
  }
}

async function main() {
  const success = await createAdminUser();
  rl.close();
  
  if (success) {
    console.log('\n✅ Admin user is ready for production management!');
  } else {
    console.log('\n❌ Failed to create admin user');
    process.exit(1);
  }
}

main().catch(console.error);
