'use client'

import { useEffect } from 'react'
import Script from 'next/script'

interface MetaPixelProps {
  pixelId: string
}

export function MetaPixel({ pixelId }: MetaPixelProps) {
  useEffect(() => {
    // Initialize Facebook Pixel
    if (typeof window !== 'undefined') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const fbq = (window as any).fbq = (window as any).fbq || function(...args: unknown[]) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ((window as any).fbq.q = (window as any).fbq.q || []).push(args)
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (!(window as any)._fbq) (window as any)._fbq = fbq
      fbq.push = fbq
      fbq.loaded = true
      fbq.version = '2.0'
      fbq.queue = []
      fbq('init', pixelId)
      fbq('track', 'PageView')
    }
  }, [pixelId])

  return (
    <>
      {/* Meta Pixel Code */}
      <Script
        id="meta-pixel"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${pixelId}');
            fbq('track', 'PageView');
          `
        }}
      />
      
      {/* NoScript fallback */}
      <noscript>
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          height="1"
          width="1"
          style={{ display: 'none' }}
          src={`https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1`}
          alt=""
        />
      </noscript>
    </>
  )
}

// Meta Pixel event tracking functions
export const trackMetaEvent = (eventName: string, parameters?: Record<string, unknown>) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  if (typeof window !== 'undefined' && (window as any).fbq) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).fbq('track', eventName, parameters)
  }
}

export const trackMetaPurchase = (value: number, currency: string = 'CHF', contentIds: string[] = []) => {
  trackMetaEvent('Purchase', {
    value: value,
    currency: currency,
    content_ids: contentIds,
    content_type: 'product'
  })
}

export const trackMetaAddToCart = (value: number, currency: string = 'CHF', contentIds: string[] = []) => {
  trackMetaEvent('AddToCart', {
    value: value,
    currency: currency,
    content_ids: contentIds,
    content_type: 'product'
  })
}

export const trackMetaViewContent = (value: number, currency: string = 'CHF', contentIds: string[] = []) => {
  trackMetaEvent('ViewContent', {
    value: value,
    currency: currency,
    content_ids: contentIds,
    content_type: 'product'
  })
}

export const trackMetaInitiateCheckout = (value: number, currency: string = 'CHF', contentIds: string[] = []) => {
  trackMetaEvent('InitiateCheckout', {
    value: value,
    currency: currency,
    content_ids: contentIds,
    content_type: 'product'
  })
}

export const trackMetaLead = () => {
  trackMetaEvent('Lead')
}

export const trackMetaCompleteRegistration = () => {
  trackMetaEvent('CompleteRegistration')
}

// Note: Using (window as any) to avoid TypeScript issues with dynamic fbq properties
