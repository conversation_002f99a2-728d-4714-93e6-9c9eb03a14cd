import { render, screen } from '@testing-library/react'
import { AdminBackButton, AdminDashboardButton } from '@/components/admin/admin-back-button'

// Mock next/link
jest.mock('next/link', () => {
  return function MockLink({ children, href }: { children: React.ReactNode; href: string }) {
    return <a href={href}>{children}</a>
  }
})

// Mock next-intl hooks
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'backToDashboard': 'Back to Dashboard'
    }
    return translations[key] || key
  },
  useLocale: () => 'en',
  NextIntlClientProvider: ({ children }: { children: React.ReactNode }) => children
}))

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const messages = {
  admin: {
    backToDashboard: 'Back to Dashboard'
  }
}

const renderWithIntl = (component: React.ReactElement) => {
  return render(component)
}

describe('AdminBackButton', () => {
  it('renders with default props', () => {
    renderWithIntl(<AdminBackButton />)
    
    const button = screen.getByRole('link')
    expect(button).toBeInTheDocument()
    expect(button).toHaveAttribute('href', '/en/admin')
    expect(screen.getByText('Back to Dashboard')).toBeInTheDocument()
  })

  it('renders with custom variant and size', () => {
    renderWithIntl(<AdminBackButton variant="default" size="lg" />)
    
    const button = screen.getByRole('link')
    expect(button).toBeInTheDocument()
  })

  it('renders without icon when showIcon is false', () => {
    renderWithIntl(<AdminBackButton showIcon={false} />)
    
    const button = screen.getByRole('link')
    expect(button).toBeInTheDocument()
    expect(screen.getByText('Back to Dashboard')).toBeInTheDocument()
  })

  it('renders without text when showText is false', () => {
    renderWithIntl(<AdminBackButton showText={false} />)
    
    const button = screen.getByRole('link')
    expect(button).toBeInTheDocument()
    // Should not have the text content
    expect(button.textContent).not.toContain('Back to Dashboard')
  })

  it('applies custom className', () => {
    renderWithIntl(<AdminBackButton className="custom-class" />)
    
    const button = screen.getByRole('link')
    expect(button).toBeInTheDocument()
  })
})

describe('AdminDashboardButton', () => {
  it('renders with dashboard icon variant', () => {
    renderWithIntl(<AdminDashboardButton />)
    
    const button = screen.getByRole('link')
    expect(button).toBeInTheDocument()
    expect(button).toHaveAttribute('href', '/en/admin')
    expect(screen.getByText('Back to Dashboard')).toBeInTheDocument()
  })
})

describe('AdminBackButton locale integration', () => {
  it('uses the current locale in href', () => {
    renderWithIntl(<AdminBackButton />)

    const button = screen.getByRole('link')
    expect(button).toBeInTheDocument()
    // Should use the mocked locale (en)
    expect(button).toHaveAttribute('href', '/en/admin')
  })
})
