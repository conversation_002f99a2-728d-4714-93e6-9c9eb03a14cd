'use client'

import { useTranslations, useLocale } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ShoppingCart } from 'lucide-react'
import { useCart } from '@/lib/cart'
import Link from 'next/link'

export function CartButton() {
  const t = useTranslations('navigation')
  const locale = useLocale()
  const { cart } = useCart()

  const itemCount = cart?.items?.reduce((sum, item) => sum + item.quantity, 0) || 0

  return (
    <Button variant="ghost" size="icon" asChild className="relative overflow-visible">
      <Link href={`/${locale}/cart`} prefetch={false}>
        <ShoppingCart className="h-5 w-5" />
        {itemCount > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
          >
            {itemCount > 99 ? '99+' : itemCount}
          </Badge>
        )}
        <span className="sr-only">
          {t('cart')} {itemCount > 0 && `(${itemCount} Artikel)`}
        </span>
      </Link>
    </Button>
  )
}
