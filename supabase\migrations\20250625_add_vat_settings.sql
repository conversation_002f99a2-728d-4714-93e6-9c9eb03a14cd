-- Add VAT/Tax configuration to site_settings
-- Migration: 20250625_add_vat_settings

-- Add VAT/Tax settings to site_settings table
ALTER TABLE site_settings 
ADD COLUMN IF NOT EXISTS vat_rate DECIMAL(5,4) DEFAULT 0.077,
ADD COLUMN IF NOT EXISTS vat_included_in_prices BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS vat_number TEXT;

-- Insert default VAT settings if no settings exist
INSERT INTO site_settings (
    store_name,
    default_currency,
    timezone,
    vat_rate,
    vat_included_in_prices,
    vat_number
) 
SELECT 
    'PrimeCaffe',
    'CHF',
    'Europe/Zurich',
    0.077,
    true,
    null
WHERE NOT EXISTS (SELECT 1 FROM site_settings);

-- Update existing settings to include VAT configuration if they exist
UPDATE site_settings 
SET 
    vat_rate = COALESCE(vat_rate, 0.077),
    vat_included_in_prices = COALESCE(vat_included_in_prices, true)
WHERE vat_rate IS NULL OR vat_included_in_prices IS NULL;
