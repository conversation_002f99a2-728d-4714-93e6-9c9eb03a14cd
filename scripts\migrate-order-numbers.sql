-- Migration script to assign friendly order numbers to existing orders
-- This script can be run manually if needed to update orders that don't have order numbers

-- Function to assign order numbers to existing orders without them
-- This maintains chronological order and year-based numbering
DO $$
DECLARE
    order_record RECORD;
    year_suffix TEXT;
    counter INTEGER := 1;
    current_year_processing TEXT := '';
    total_updated INTEGER := 0;
BEGIN
    RAISE NOTICE 'Starting order number migration for existing orders...';
    
    -- Process existing orders without order numbers in chronological order
    FOR order_record IN 
        SELECT id, created_at 
        FROM orders 
        WHERE order_number IS NULL 
        ORDER BY created_at ASC
    LOOP
        -- Get year suffix for this order
        year_suffix := RIGHT(EXTRACT(YEAR FROM order_record.created_at)::TEXT, 2);
        
        -- Reset counter if we're in a new year
        IF current_year_processing != year_suffix THEN
            current_year_processing := year_suffix;
            counter := 1;
            RAISE NOTICE 'Processing orders for year 20%', year_suffix;
        END IF;
        
        -- Update the order with the generated number
        UPDATE orders 
        SET order_number = 'PC' || year_suffix || LPAD(counter::TEXT, 4, '0')
        WHERE id = order_record.id;
        
        total_updated := total_updated + 1;
        counter := counter + 1;
        
        -- Log progress every 100 orders
        IF total_updated % 100 = 0 THEN
            RAISE NOTICE 'Updated % orders so far...', total_updated;
        END IF;
    END LOOP;
    
    -- Update the sequence to continue from where we left off for current year
    IF current_year_processing = RIGHT(EXTRACT(YEAR FROM NOW())::TEXT, 2) THEN
        PERFORM setval('order_number_seq', counter);
        RAISE NOTICE 'Updated sequence to continue from %', counter;
    END IF;
    
    RAISE NOTICE 'Migration completed! Updated % orders total.', total_updated;
    
    -- Show summary of order numbers by year
    RAISE NOTICE 'Order number summary by year:';
    FOR order_record IN
        SELECT 
            SUBSTRING(order_number FROM 3 FOR 2) as year_suffix,
            COUNT(*) as order_count,
            MIN(order_number) as first_order,
            MAX(order_number) as last_order
        FROM orders 
        WHERE order_number IS NOT NULL
        GROUP BY SUBSTRING(order_number FROM 3 FOR 2)
        ORDER BY year_suffix
    LOOP
        RAISE NOTICE 'Year 20%: % orders (% to %)', 
            order_record.year_suffix, 
            order_record.order_count,
            order_record.first_order,
            order_record.last_order;
    END LOOP;
    
END $$;

-- Verify the migration results
SELECT 
    'Migration Results' as status,
    COUNT(*) as total_orders,
    COUNT(order_number) as orders_with_numbers,
    COUNT(*) - COUNT(order_number) as orders_without_numbers
FROM orders;

-- Show sample of migrated orders
SELECT 
    id,
    order_number,
    email,
    created_at
FROM orders 
WHERE order_number IS NOT NULL
ORDER BY created_at ASC
LIMIT 10;
