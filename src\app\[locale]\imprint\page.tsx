"use client";

import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building, Mail, Phone } from 'lucide-react';

export default function ImprintPage() {
  const t = useTranslations('legal.imprint');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-6">
            <Building className="h-8 w-8 text-purple-600" />
          </div>
          <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>{t('companyInfo.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Firmenname</h4>
                  <p>{t('companyInfo.name')}</p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Adresse</h4>
                  <p>
                    {t('companyInfo.address')}<br />
                    {t('companyInfo.city')}<br />
                    {t('companyInfo.country')}
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">{t('registration.title')}</h4>
                  <p>{t('registration.number')}</p>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Mehrwertsteuer-Nummer</h4>
                  <p>CHE-123.456.789 MWST</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Kontaktinformationen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">E-Mail</p>
                    <p className="text-sm text-muted-foreground">{t('companyInfo.email')}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Telefon</p>
                    <p className="text-sm text-muted-foreground">{t('companyInfo.phone')}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('management.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{t('management.ceo')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('responsibility.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p style={{ whiteSpace: 'pre-line' }}>
                {t('responsibility.content')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('disclaimer.title')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('disclaimer.content')}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
