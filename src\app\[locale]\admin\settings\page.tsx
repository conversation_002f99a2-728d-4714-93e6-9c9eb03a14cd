'use client'

import { createClient } from '@/lib/supabase/client'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Store,
  Mail,
  CreditCard,
  Shield,
  Globe,
  Bell,
  Database,
  Calculator
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { useEffect, useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import { AdminBackButton } from '@/components/admin/admin-back-button'

interface SiteSettings {
  id?: string;
  store_name?: string;
  store_description?: string;
  contact_email?: string;
  contact_phone?: string;
  email_order_confirmation?: boolean;
  email_shipping_notification?: boolean;
  email_newsletter?: boolean;
  payment_stripe_enabled?: boolean;
  payment_paypal_enabled?: boolean;
  payment_twint_enabled?: boolean;
  payment_invoice_enabled?: boolean;
  security_2fa_enabled?: boolean;
  security_rate_limiting?: boolean;
  security_session_timeout?: number;
  default_language?: string;
  default_currency?: string;
  timezone?: string;
  vat_rate?: number;
  vat_rate_coffee?: number;
  vat_rate_accessories?: number;
  use_category_vat?: boolean;
  vat_included_in_prices?: boolean;
  vat_number?: string | null;
  notifications_low_stock?: boolean;
  notifications_new_orders?: boolean;
  notifications_system_alerts?: boolean;
}

export default function AdminSettingsPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin.settingsPage')
  const { toast } = useToast()
  const [settings, setSettings] = useState<SiteSettings>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [useCategoryVat, setUseCategoryVat] = useState(false)


  useEffect(() => {
    checkAuthAndLoadSettings()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuthAndLoadSettings = async () => {
    const supabase = createClient()

    // Check if user is authenticated and is admin
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      router.push(`/${locale}/login`)
      return
    }

    // Check admin status
    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      router.push(`/${locale}`)
      return
    }



    // Get current settings
    const { data: settingsData, error } = await supabase
      .from('site_settings')
      .select('*')
      .maybeSingle()

    if (error) {
      console.error('Error fetching settings:', error)
    } else {
      // Set default values if no settings exist yet
      const defaultSettings = {
        store_name: 'PrimeCaffe',
        store_description: '',
        contact_email: '<EMAIL>',
        contact_phone: '+41 44 123 45 67',
        default_language: 'de',
        default_currency: 'CHF',
        timezone: 'Europe/Zurich'
      }
      setSettings(settingsData || defaultSettings)
      setUseCategoryVat(settingsData?.use_category_vat ?? false)
    }

    setLoading(false)
  }

  const saveSettings = async (section: string, data: Partial<SiteSettings>) => {
    setSaving(true)

    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Errore nel salvataggio')
      }

      const updatedSettings = await response.json()
      setSettings(prev => ({ ...prev, ...updatedSettings }))

      toast({
        title: t('saveSuccess'),
        description: t('saveSuccessMessage'),
      })
    } catch (error) {
      console.error('Error saving settings:', error)
      toast({
        title: t('saveError'),
        description: error instanceof Error ? error.message : t('saveErrorMessage'),
        variant: 'destructive',
      })
    } finally {
      setSaving(false)
    }
  }

  const handleStoreSettingsSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement)
    const data = {
      store_name: formData.get('store-name') as string,
      store_description: formData.get('store-description') as string,
      contact_email: formData.get('store-email') as string,
      contact_phone: formData.get('store-phone') as string,
    }
    saveSettings('store', data)
  }


  const handleLocalizationSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement)
    const data = {
      default_language: formData.get('default-language') as string,
      default_currency: formData.get('default-currency') as string,
      timezone: formData.get('timezone') as string,
    }
    saveSettings('localization', data)
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Store Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Store className="h-5 w-5" />
              {t('storeSettings.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleStoreSettingsSubmit} className="space-y-4">
              <div>
                <Label htmlFor="store-name">{t('storeSettings.storeName')}</Label>
                <Input
                  id="store-name"
                  name="store-name"
                  defaultValue={settings?.store_name || "PrimeCaffe"}
                  placeholder={t('storeSettings.storeNamePlaceholder')}
                />
              </div>
              <div>
                <Label htmlFor="store-description">{t('storeSettings.storeDescription')}</Label>
                <Textarea
                  id="store-description"
                  name="store-description"
                  defaultValue={settings?.store_description || ""}
                  placeholder={t('storeSettings.storeDescriptionPlaceholder')}
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="store-email">{t('storeSettings.contactEmail')}</Label>
                <Input
                  id="store-email"
                  name="store-email"
                  type="email"
                  defaultValue={settings?.contact_email || "<EMAIL>"}
                  placeholder={t('storeSettings.contactEmailPlaceholder')}
                />
              </div>
              <div>
                <Label htmlFor="store-phone">{t('storeSettings.contactPhone')}</Label>
                <Input
                  id="store-phone"
                  name="store-phone"
                  defaultValue={settings?.contact_phone || "+41 79 342 65 74"}
                  placeholder={t('storeSettings.contactPhonePlaceholder')}
                />
              </div>
              <Button type="submit" disabled={saving}>
                {saving ? t('saving') : t('save')}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Email Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {t('emailSettings.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('emailSettings.orderConfirmations')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('emailSettings.orderConfirmationsDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.email_order_confirmation ?? true}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('emailSettings.shippingNotifications')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('emailSettings.shippingNotificationsDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.email_shipping_notification ?? true}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('emailSettings.newsletter')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('emailSettings.newsletterDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.email_newsletter ?? false}
                disabled
                className="cursor-not-allowed"
              />
            </div>
          </CardContent>
        </Card>

        {/* Payment Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              {t('paymentSettings.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>{t('paymentSettings.stripe')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('paymentSettings.stripeDesc')} - {t('functional')}
                </p>
              </div>
              <Switch
                checked={settings?.payment_stripe_enabled ?? true}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('paymentSettings.paypal')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('paymentSettings.paypalDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.payment_paypal_enabled ?? false}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('paymentSettings.twint')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('paymentSettings.twintDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.payment_twint_enabled ?? false}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('paymentSettings.invoice')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('paymentSettings.invoiceDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.payment_invoice_enabled ?? false}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="mt-4 p-3 bg-blue-50 rounded-md">
              <p className="text-sm text-blue-800">
                {t('paymentSettings.note')}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {t('securitySettings.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('securitySettings.twoFactor')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('securitySettings.twoFactorDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.security_2fa_enabled ?? false}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('securitySettings.rateLimiting')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('securitySettings.rateLimitingDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.security_rate_limiting ?? true}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="opacity-50">
              <Label htmlFor="session-timeout" className="text-muted-foreground">{t('securitySettings.sessionTimeout')}</Label>
              <Input
                id="session-timeout"
                type="number"
                value={settings?.security_session_timeout || 60}
                placeholder="60"
                disabled
                className="cursor-not-allowed"
              />
              <p className="text-sm text-muted-foreground mt-1">
                {t('notImplemented')}
              </p>
            </div>
            <div className="mt-4 p-3 bg-yellow-50 rounded-md">
              <p className="text-sm text-yellow-800">
                {t('securitySettings.note')}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Localization Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              {t('localization.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLocalizationSubmit} className="space-y-4">
              <div>
                <Label htmlFor="default-language">{t('localization.defaultLanguage')}</Label>
                <select
                  id="default-language"
                  name="default-language"
                  className="w-full p-2 border rounded-md"
                  defaultValue={settings?.default_language || "de"}
                >
                  <option value="de">{t('localization.languages.de')}</option>
                  <option value="fr">{t('localization.languages.fr')}</option>
                  <option value="it">{t('localization.languages.it')}</option>
                </select>
              </div>
              <div>
                <Label htmlFor="default-currency">{t('localization.defaultCurrency')}</Label>
                <select
                  id="default-currency"
                  name="default-currency"
                  className="w-full p-2 border rounded-md"
                  defaultValue={settings?.default_currency || "CHF"}
                >
                  <option value="CHF">{t('localization.currencies.chf')}</option>
                  <option value="EUR">EUR (Euro)</option>
                  <option value="USD">USD (US Dollar)</option>
                </select>
              </div>
              <div>
                <Label htmlFor="timezone">{t('localization.timezone')}</Label>
                <select
                  id="timezone"
                  name="timezone"
                  className="w-full p-2 border rounded-md"
                  defaultValue={settings?.timezone || "Europe/Zurich"}
                >
                  <option value="Europe/Zurich">Europe/Zurich</option>
                  <option value="Europe/Berlin">Europe/Berlin</option>
                  <option value="Europe/Paris">Europe/Paris</option>
                  <option value="Europe/Rome">Europe/Rome</option>
                </select>
              </div>
              <Button type="submit" disabled={saving}>
                {saving ? t('saving') : t('save')}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* VAT/Tax Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              {t('vat.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={(e) => {
              e.preventDefault()
              const formData = new FormData(e.currentTarget)
              const useCategoryVat = formData.get('use-category-vat') === 'on'
              const vatData = {
                vat_rate: parseFloat(formData.get('vat-rate') as string) / 100, // Convert percentage to decimal
                vat_rate_coffee: parseFloat(formData.get('vat-rate-coffee') as string) / 100,
                vat_rate_accessories: parseFloat(formData.get('vat-rate-accessories') as string) / 100,
                use_category_vat: useCategoryVat,
                vat_included_in_prices: formData.get('vat-included') === 'on',
                vat_number: formData.get('vat-number') as string || null
              }
              saveSettings('vat', vatData)
            }} className="space-y-6">
              {/* Category VAT Toggle */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="use-category-vat"
                  name="use-category-vat"
                  checked={useCategoryVat}
                  onCheckedChange={(checked) => setUseCategoryVat(checked as boolean)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label htmlFor="use-category-vat" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    {t('vat.useCategoryVat')}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {t('vat.useCategoryVatDesc')}
                  </p>
                </div>
              </div>

              {/* General VAT Rate (shown when category VAT is disabled) */}
              <div className={`grid md:grid-cols-2 gap-4 ${useCategoryVat ? 'opacity-50' : ''}`}>
                <div>
                  <Label htmlFor="vat-rate">{t('vat.vatRate')}</Label>
                  <div className="relative">
                    <Input
                      id="vat-rate"
                      name="vat-rate"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      defaultValue={((settings?.vat_rate || 0.077) * 100).toFixed(2)}
                      className="pr-8"
                      disabled={useCategoryVat}
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">%</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('vat.vatRateDesc')}
                  </p>
                </div>
                <div>
                  <Label htmlFor="vat-number">{t('vat.vatNumber')}</Label>
                  <Input
                    id="vat-number"
                    name="vat-number"
                    defaultValue={settings?.vat_number || ''}
                    placeholder="CHE-123.456.789 MWST"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('vat.vatNumberDesc')}
                  </p>
                </div>
              </div>

              {/* Category-specific VAT Rates */}
              <div className={`grid md:grid-cols-2 gap-4 ${!useCategoryVat ? 'opacity-50' : ''}`}>
                <div>
                  <Label htmlFor="vat-rate-coffee">{t('vat.vatRateCoffee')}</Label>
                  <div className="relative">
                    <Input
                      id="vat-rate-coffee"
                      name="vat-rate-coffee"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      defaultValue={((settings?.vat_rate_coffee || 0.077) * 100).toFixed(2)}
                      className="pr-8"
                      disabled={!useCategoryVat}
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">%</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('vat.vatRateCoffeeDesc')}
                  </p>
                </div>
                <div>
                  <Label htmlFor="vat-rate-accessories">{t('vat.vatRateAccessories')}</Label>
                  <div className="relative">
                    <Input
                      id="vat-rate-accessories"
                      name="vat-rate-accessories"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      defaultValue={((settings?.vat_rate_accessories || 0.077) * 100).toFixed(2)}
                      className="pr-8"
                      disabled={!useCategoryVat}
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">%</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('vat.vatRateAccessoriesDesc')}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="vat-included"
                  name="vat-included"
                  defaultChecked={settings?.vat_included_in_prices ?? true}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label htmlFor="vat-included" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    {t('vat.vatIncluded')}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {t('vat.vatIncludedDesc')}
                  </p>
                </div>
              </div>
              <Button type="submit" disabled={saving}>
                {saving ? t('saving') : t('save')}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              {t('notifications.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('notifications.newOrders')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('notifications.newOrdersDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.notifications_new_orders ?? true}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('notifications.lowStock')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('notifications.lowStockDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.notifications_low_stock ?? true}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="flex items-center justify-between opacity-50">
              <div>
                <Label className="text-muted-foreground">{t('notifications.systemUpdates')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('notifications.systemUpdatesDesc')} - {t('notImplemented')}
                </p>
              </div>
              <Switch
                checked={settings?.notifications_system_alerts ?? true}
                disabled
                className="cursor-not-allowed"
              />
            </div>
            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <p className="text-sm text-gray-600">
                {t('notifications.note')}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* System Information */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            {t('systemInfo.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <Label>{t('systemInfo.version')}</Label>
              <p className="text-sm text-muted-foreground">PrimeCaffe v1.0.0</p>
            </div>
            <div>
              <Label>{t('systemInfo.database')}</Label>
              <p className="text-sm text-muted-foreground">PostgreSQL (Supabase)</p>
            </div>
            <div>
              <Label>{t('systemInfo.lastBackup')}</Label>
              <p className="text-sm text-muted-foreground">
                {new Date().toLocaleDateString(locale === 'de' ? 'de-CH' : locale === 'fr' ? 'fr-CH' : 'it-CH')} - {t('systemInfo.automatic')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
