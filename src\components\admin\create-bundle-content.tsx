'use client'

import BundleForm from '@/components/admin/bundle-form'
import { useTranslations } from 'next-intl'
import { Gift, TrendingUp } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { AdminBackButton } from './admin-back-button'

interface CreateBundleContentProps {
  locale: string
}

export default function CreateBundleContent({ locale }: CreateBundleContentProps) {
  const t = useTranslations('admin.bundleManagement')

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      {/* Header Section */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('createTitle')}</h1>
          <p className="text-muted-foreground">
            {t('createSubtitle')}
          </p>
        </div>
      </div>

      {/* Info Cards */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold">Simulazione Margini</h3>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              La sezione <span className="font-semibold text-primary">&quot;Extra&quot;</span> ti permette di simulare un acquisto completo
              per calcolare esattamente i tuoi margini di profitto e determinare il prezzo ottimale del bundle.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
                <Gift className="h-5 w-5 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold">Prodotti Regalo</h3>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              Seleziona qualsiasi prodotto disponibile come regalo per aumentare
              il valore percepito del bundle e la soddisfazione del cliente.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Form Container */}
      <Card>
        <CardContent className="pt-6">
          <BundleForm locale={locale} />
        </CardContent>
      </Card>
    </div>
  )
}
