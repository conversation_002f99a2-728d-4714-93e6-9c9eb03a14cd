-- Add product templates and tag system
-- Migration: 20250625_add_product_templates_and_tags

-- Product templates table
CREATE TABLE product_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    title_template TEXT NOT NULL, -- Template string with placeholders like "{brand} {coffee_type} {blend}"
    category product_category NOT NULL,
    coffee_type coffee_type,
    brand TEXT,
    blend TEXT,
    machine_compatibility TEXT[],
    pack_quantity INTEGER,
    pack_weight_grams INTEGER,
    price DECIMAL(10,2),
    discount_price DECIMAL(10,2),
    cost_per_espresso DECIMAL(10,4),
    inventory_count INTEGER DEFAULT 0,
    purchase_cost DECIMAL(10,2),
    is_available BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE, -- Whether this is a default template
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Product title tags table - for tracking which tags are used in titles
CREATE TABLE product_title_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    tag_name TEXT NOT NULL, -- e.g., 'brand', 'coffee_type', 'blend', 'pack_quantity'
    tag_value TEXT NOT NULL, -- e.g., 'Lavazza', 'capsules', 'Crema e Gusto', '100'
    position INTEGER NOT NULL, -- Order in the title
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(product_id, tag_name)
);

-- Add title_template field to products table to store the template used
ALTER TABLE products ADD COLUMN title_template TEXT;
ALTER TABLE products ADD COLUMN template_id UUID REFERENCES product_templates(id);

-- Create indexes for better performance
CREATE INDEX idx_product_title_tags_product_id ON product_title_tags(product_id);
CREATE INDEX idx_product_title_tags_tag_name ON product_title_tags(tag_name);
CREATE INDEX idx_product_templates_category ON product_templates(category);
CREATE INDEX idx_products_template_id ON products(template_id);

-- Insert some default templates
INSERT INTO product_templates (
    name, 
    description, 
    title_template, 
    category, 
    coffee_type, 
    is_default
) VALUES 
(
    'Standard Coffee Template',
    'Default template for coffee products',
    '{brand} {coffee_type} - {blend} ({pack_quantity} Stück)',
    'coffee',
    'capsules',
    true
),
(
    'Coffee Beans Template',
    'Template for coffee beans',
    '{brand} {blend} Kaffeebohnen - {pack_weight_grams}g',
    'coffee',
    'beans',
    true
),
(
    'Accessories Template',
    'Default template for accessories',
    '{brand} {title} - Zubehör',
    'accessories',
    null,
    true
);

-- Function to generate title from template and product data
CREATE OR REPLACE FUNCTION generate_product_title(
    template_text TEXT,
    product_data JSONB
) RETURNS TEXT AS $$
DECLARE
    result_title TEXT;
    tag_name TEXT;
    tag_value TEXT;
BEGIN
    result_title := template_text;
    
    -- Replace common placeholders
    IF product_data ? 'brand' AND product_data->>'brand' IS NOT NULL THEN
        result_title := REPLACE(result_title, '{brand}', product_data->>'brand');
    END IF;
    
    IF product_data ? 'coffee_type' AND product_data->>'coffee_type' IS NOT NULL THEN
        result_title := REPLACE(result_title, '{coffee_type}', 
            CASE product_data->>'coffee_type'
                WHEN 'capsules' THEN 'Kapseln'
                WHEN 'pods' THEN 'Pads'
                WHEN 'beans' THEN 'Bohnen'
                WHEN 'ground' THEN 'Gemahlen'
                ELSE product_data->>'coffee_type'
            END
        );
    END IF;
    
    IF product_data ? 'blend' AND product_data->>'blend' IS NOT NULL THEN
        result_title := REPLACE(result_title, '{blend}', product_data->>'blend');
    END IF;
    
    IF product_data ? 'pack_quantity' AND product_data->>'pack_quantity' IS NOT NULL THEN
        result_title := REPLACE(result_title, '{pack_quantity}', product_data->>'pack_quantity');
    END IF;
    
    IF product_data ? 'pack_weight_grams' AND product_data->>'pack_weight_grams' IS NOT NULL THEN
        result_title := REPLACE(result_title, '{pack_weight_grams}', product_data->>'pack_weight_grams');
    END IF;
    
    IF product_data ? 'title' AND product_data->>'title' IS NOT NULL THEN
        result_title := REPLACE(result_title, '{title}', product_data->>'title');
    END IF;
    
    -- Clean up any remaining placeholders
    result_title := REGEXP_REPLACE(result_title, '\{[^}]*\}', '', 'g');
    
    -- Clean up extra spaces and dashes
    result_title := REGEXP_REPLACE(result_title, '\s+', ' ', 'g');
    result_title := REGEXP_REPLACE(result_title, '\s*-\s*-\s*', ' - ', 'g');
    result_title := REGEXP_REPLACE(result_title, '^\s*-\s*|\s*-\s*$', '', 'g');
    result_title := TRIM(result_title);
    
    RETURN result_title;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update title when product data changes
CREATE OR REPLACE FUNCTION update_product_title_from_template()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update if title_template is set
    IF NEW.title_template IS NOT NULL THEN
        NEW.title := generate_product_title(
            NEW.title_template,
            jsonb_build_object(
                'brand', NEW.brand,
                'coffee_type', NEW.coffee_type,
                'blend', NEW.blend,
                'pack_quantity', NEW.pack_quantity,
                'pack_weight_grams', NEW.pack_weight_grams,
                'title', NEW.title
            )
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_update_product_title ON products;
CREATE TRIGGER trigger_update_product_title
    BEFORE INSERT OR UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_product_title_from_template();
