import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// GET - Fetch user's gift claims and available products
export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's pending gift claims
    const { data: claims } = await supabase
      .from('user_gift_claims')
      .select(`
        *,
        gift_rewards!inner(*)
      `)
      .eq('user_id', user.id)
      .eq('status', 'pending')
      .order('claimed_at', { ascending: false })

    // For each claim, get the product details for the gift options
    const enrichedClaims = []
    if (claims) {
      for (const claim of claims) {
        const reward = claim.gift_rewards

        // Get product details for each option
        const productIds = [
          reward.option_1_product_id,
          reward.option_2_product_id,
          reward.option_3_product_id
        ].filter(<PERSON><PERSON>an)

        if (productIds.length > 0) {
          const { data: products } = await supabase
            .from('products')
            .select('id, title, images, price')
            .in('id', productIds)

          // Map products to the reward options
          reward.option_1_product = products?.find(p => p.id === reward.option_1_product_id) || null
          reward.option_2_product = products?.find(p => p.id === reward.option_2_product_id) || null
          reward.option_3_product = products?.find(p => p.id === reward.option_3_product_id) || null
        }

        enrichedClaims.push(claim)
      }
    }

    return NextResponse.json({
      claims: enrichedClaims
    })
  } catch (error) {
    console.error('Error fetching gift claims:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
