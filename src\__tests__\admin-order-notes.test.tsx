import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'

// Mock next-intl
const mockUseLocale = jest.fn(() => 'it')
const mockUseTranslations = jest.fn(() => (key: string) => {
  const translations: Record<string, string> = {
    'admin.orderDetail.notes': 'Note',
    'admin.orderDetail.orderItems': 'Articoli Ordine',
    'admin.orderDetail.quantity': 'Quantità'
  }
  return translations[key] || key
})

jest.mock('next-intl', () => ({
  useLocale: () => mockUseLocale(),
  useTranslations: () => mockUseTranslations(),
}))

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
  useParams: () => ({ id: 'test-order-id' }),
}))

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: jest.fn().mockResolvedValue({
      data: { user: { id: 'admin-user-id' } },
      error: null
    })
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn().mockResolvedValue({
          data: {
            id: 'test-order-id',
            order_number: 'ORD-001',
            email: '<EMAIL>',
            status: 'confirmed',
            subtotal: 100,
            shipping_cost: 10,
            tax_amount: 8.1,
            discount_amount: 0,
            total_amount: 118.1,
            currency: 'CHF',
            payment_status: 'paid',
            shipping_address: {
              firstName: 'Mario',
              lastName: 'Rossi',
              street: 'Via Roma 1',
              city: 'Milano',
              postalCode: '20100',
              country: 'IT'
            },
            billing_address: {
              firstName: 'Mario',
              lastName: 'Rossi',
              street: 'Via Roma 1',
              city: 'Milano',
              postalCode: '20100',
              country: 'IT'
            },
            notes: 'Questa è una nota di test per l\'ordine',
            created_at: '2024-01-01T10:00:00Z',
            updated_at: '2024-01-01T10:00:00Z',
            order_items: [
              {
                id: 'item-1',
                quantity: 2,
                unit_price: 50,
                total_price: 100,
                products: {
                  title: 'Caffè Test'
                }
              }
            ]
          },
          error: null
        })
      }))
    }))
  }))
}

jest.mock('@/lib/supabase/client', () => ({
  createClient: () => mockSupabase
}))

// Mock toast
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}))

// Mock currency formatter
jest.mock('@/lib/utils', () => ({
  formatCurrency: (amount: number) => `CHF ${amount.toFixed(2)}`
}))

// Create a simplified version of the order detail component for testing
const OrderDetailTestComponent = () => {
  const order = {
    id: 'test-order-id',
    order_number: 'ORD-001',
    notes: 'Questa è una nota di test per l\'ordine',
    order_items: [
      {
        id: 'item-1',
        quantity: 2,
        unit_price: 50,
        total_price: 100,
        products: {
          title: 'Caffè Test'
        }
      }
    ]
  }

  return (
    <div>
      {/* Order Items */}
      <div data-testid="order-items">
        <h3>Articoli Ordine</h3>
        {order.order_items.map((item) => (
          <div key={item.id}>
            <h4>{item.products.title}</h4>
            <p>Quantità: {item.quantity} × CHF {item.unit_price.toFixed(2)}</p>
          </div>
        ))}
      </div>

      {/* Order Notes - This is what we're testing */}
      {order.notes && (
        <div data-testid="order-notes">
          <h3>Note</h3>
          <div>
            <p>{order.notes}</p>
          </div>
        </div>
      )}
    </div>
  )
}

describe('Admin Order Notes Display and Edit', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should display order notes when notes exist', () => {
    render(<OrderDetailTestComponent />)

    // Check that the notes section is rendered
    const notesSection = screen.getByTestId('order-notes')
    expect(notesSection).toBeInTheDocument()

    // Check that the notes title is displayed
    expect(screen.getByText('Note')).toBeInTheDocument()

    // Check that the actual note content is displayed
    expect(screen.getByText('Questa è una nota di test per l\'ordine')).toBeInTheDocument()
  })

  test('should not display notes section when no notes exist', () => {
    // Create component without notes
    const OrderDetailWithoutNotes = () => {
      const order = {
        id: 'test-order-id',
        order_number: 'ORD-001',
        notes: null, // No notes
        order_items: []
      }

      return (
        <div>
          {order.notes && (
            <div data-testid="order-notes">
              <h3>Note</h3>
              <div>
                <p>{order.notes}</p>
              </div>
            </div>
          )}
        </div>
      )
    }

    render(<OrderDetailWithoutNotes />)
    
    // Check that the notes section is NOT rendered
    const notesSection = screen.queryByTestId('order-notes')
    expect(notesSection).not.toBeInTheDocument()
  })

  test('should display notes with proper formatting', () => {
    // Test with multiline notes
    const OrderDetailWithMultilineNotes = () => {
      const order = {
        notes: 'Prima riga della nota\nSeconda riga della nota\nTerza riga della nota'
      }

      return (
        <div>
          {order.notes && (
            <div data-testid="order-notes">
              <h3>Note</h3>
              <div>
                <p style={{ whiteSpace: 'pre-wrap' }}>{order.notes}</p>
              </div>
            </div>
          )}
        </div>
      )
    }

    render(<OrderDetailWithMultilineNotes />)
    
    // Check that multiline notes are displayed
    expect(screen.getByText(/Prima riga della nota/)).toBeInTheDocument()
    expect(screen.getByText(/Seconda riga della nota/)).toBeInTheDocument()
    expect(screen.getByText(/Terza riga della nota/)).toBeInTheDocument()
  })

  test('should allow editing notes in admin interface', () => {
    // Test component with editable notes field
    const EditableNotesComponent = () => {
      const [notes, setNotes] = React.useState('Nota iniziale')

      return (
        <div>
          <label htmlFor="notes">Note</label>
          <textarea
            id="notes"
            data-testid="notes-textarea"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Inserisci note aggiuntive..."
          />
          <div data-testid="notes-display">{notes}</div>
        </div>
      )
    }

    render(<EditableNotesComponent />)

    // Check that the textarea is present
    const textarea = screen.getByTestId('notes-textarea')
    expect(textarea).toBeInTheDocument()

    // Check initial value
    expect(textarea).toHaveValue('Nota iniziale')

    // Check that we can change the value
    fireEvent.change(textarea, { target: { value: 'Nota modificata' } })
    expect(textarea).toHaveValue('Nota modificata')

    // Check that the display is updated
    expect(screen.getByTestId('notes-display')).toHaveTextContent('Nota modificata')
  })
})
