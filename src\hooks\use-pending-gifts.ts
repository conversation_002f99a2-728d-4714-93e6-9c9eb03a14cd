import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

export function usePendingGifts(userId?: string) {
  const [pendingCount, setPendingCount] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!userId) {
      setPendingCount(0)
      setLoading(false)
      return
    }

    const fetchPendingGifts = async () => {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('user_gift_claims')
          .select('id')
          .eq('user_id', userId)
          .eq('status', 'pending')

        if (error) {
          console.error('Error fetching pending gifts:', error)
          setPendingCount(0)
        } else {
          setPendingCount(data?.length || 0)
        }
      } catch (error) {
        console.error('Error fetching pending gifts:', error)
        setPendingCount(0)
      } finally {
        setLoading(false)
      }
    }

    fetchPendingGifts()
  }, [userId])

  return { pendingCount, loading }
}
