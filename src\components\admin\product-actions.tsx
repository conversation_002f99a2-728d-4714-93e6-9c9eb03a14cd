'use client'

import { useState } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { MoreHorizontal, Edit, Trash2, ToggleLeft, ToggleRight, Eye } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'

interface Product {
  id: string
  title: string
  is_available: boolean
}

interface ProductActionsProps {
  product: Product
}

export default function ProductActions({ product }: ProductActionsProps) {
  const [loading, setLoading] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const t = useTranslations('admin')
  const locale = useLocale()

  const handleToggleAvailable = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/products/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: product.id,
          is_available: !product.is_available
        }),
      })

      if (response.ok) {
        toast({
          title: t('products.actions.statusUpdated'),
          description: t('products.actions.statusDescription', {
            status: !product.is_available ? t('products.actions.activated') : t('products.actions.deactivated')
          }),
        })
        router.refresh()
      } else {
        throw new Error('Failed to update product')
      }
    } catch (error) {
      console.error('Error updating product:', error)
      toast({
        title: t('common.error'),
        description: t('products.actions.updateError'),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/products/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: product.id }),
      })

      if (response.ok) {
        toast({
          title: t('products.actions.deleted'),
          description: t('products.actions.deleteSuccess'),
        })
        router.refresh()
      } else {
        throw new Error('Failed to delete product')
      }
    } catch (error) {
      console.error('Error deleting product:', error)
      toast({
        title: t('common.error'),
        description: t('products.actions.deleteError'),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
      setShowDeleteDialog(false)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t('products.actions.openMenu')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={() => router.push(`/${locale}/admin/products/view/${product.id}`)}
          >
            <Eye className="mr-2 h-4 w-4" />
            {t('products.actions.view')}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => router.push(`/${locale}/admin/products/edit/${product.id}`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            {t('products.actions.edit')}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleToggleAvailable} disabled={loading}>
            {product.is_available ? (
              <>
                <ToggleLeft className="mr-2 h-4 w-4" />
                {t('products.actions.deactivate')}
              </>
            ) : (
              <>
                <ToggleRight className="mr-2 h-4 w-4" />
                {t('products.actions.activate')}
              </>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {t('products.actions.delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('products.actions.deleteTitle')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('products.actions.deleteConfirm', { title: product.title })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={loading}
            >
              {loading ? t('products.actions.deleting') : t('products.actions.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
