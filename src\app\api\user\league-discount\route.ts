import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { calculateLeague, getLeagueDiscount } from '@/lib/gamification'

export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ discount: null })
    }

    // Get user's current level and points
    const { data: userProfile } = await supabase
      .from('users')
      .select('current_level, total_points')
      .eq('id', user.id)
      .single()

    if (!userProfile) {
      return NextResponse.json({ discount: null })
    }

    const currentLevel = userProfile.current_level || 1
    const league = calculateLeague(currentLevel)
    const discountPercentage = await getLeagueDiscount(league)

    if (discountPercentage === 0) {
      return NextResponse.json({ discount: null })
    }

    // Get level name for display
    const { data: levelData } = await supabase
      .from('user_levels')
      .select('name')
      .eq('level', currentLevel)
      .single()

    const discount = {
      percentage: discountPercentage,
      league: league,
      levelName: levelData?.name || `Level ${currentLevel}`,
      userLevel: currentLevel
    }

    return NextResponse.json({ discount })
  } catch (error) {
    console.error('Error getting user league discount:', error)
    return NextResponse.json({ discount: null })
  }
}
