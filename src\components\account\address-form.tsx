'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { X } from 'lucide-react';
import type { Database } from '@/lib/supabase/database.types';

type UserAddress = Database['public']['Tables']['user_addresses']['Row'];

const addressSchema = z.object({
  type: z.enum(['billing', 'shipping']),
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  company: z.string().optional(),
  street_address: z.string().min(1, 'Street address is required'),
  city: z.string().min(1, 'City is required'),
  postal_code: z.string().min(1, 'Postal code is required'),
  country: z.string().min(2, 'Country is required'),
  is_default: z.boolean().optional(),
});

type AddressFormData = z.infer<typeof addressSchema>;

interface AddressFormProps {
  address?: UserAddress;
  onSubmit: (data: AddressFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'add' | 'edit';
}

const countries = [
  { code: 'CH', name: 'Switzerland' },
  { code: 'DE', name: 'Germany' },
  { code: 'AT', name: 'Austria' },
  { code: 'FR', name: 'France' },
  { code: 'IT', name: 'Italy' },
];

export default function AddressForm({ 
  address, 
  onSubmit, 
  onCancel, 
  isLoading = false, 
  mode 
}: AddressFormProps) {
  const t = useTranslations('account.addresses');
  const tForm = useTranslations('account.addresses.form');
  const tValidation = useTranslations('account.addresses.validation');

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
    defaultValues: address ? {
      type: address.type,
      first_name: address.first_name,
      last_name: address.last_name,
      company: address.company || '',
      street_address: address.street_address,
      city: address.city,
      postal_code: address.postal_code,
      country: address.country,
      is_default: address.is_default,
    } : {
      type: 'shipping' as const,
      first_name: '',
      last_name: '',
      company: '',
      street_address: '',
      city: '',
      postal_code: '',
      country: 'CH',
      is_default: false,
    }
  });

  const watchedType = watch('type');
  const watchedCountry = watch('country');
  const watchedIsDefault = watch('is_default');

  const handleFormSubmit = async (data: AddressFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg">
          {mode === 'add' ? t('addNew') : t('edit')}
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          disabled={isSubmitting || isLoading}
        >
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* Address Type */}
          <div className="space-y-2">
            <Label htmlFor="type">{tForm('type')}</Label>
            <Select
              value={watchedType}
              onValueChange={(value: 'billing' | 'shipping') => setValue('type', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="shipping">{tForm('typeShipping')}</SelectItem>
                <SelectItem value="billing">{tForm('typeBilling')}</SelectItem>
              </SelectContent>
            </Select>
            {errors.type && (
              <p className="text-sm text-red-600">{errors.type.message}</p>
            )}
          </div>

          {/* Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="first_name">{tForm('firstName')} *</Label>
              <Input
                id="first_name"
                {...register('first_name')}
                placeholder={tForm('firstNamePlaceholder')}
                disabled={isSubmitting || isLoading}
              />
              {errors.first_name && (
                <p className="text-sm text-red-600">{tValidation('firstNameRequired')}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="last_name">{tForm('lastName')} *</Label>
              <Input
                id="last_name"
                {...register('last_name')}
                placeholder={tForm('lastNamePlaceholder')}
                disabled={isSubmitting || isLoading}
              />
              {errors.last_name && (
                <p className="text-sm text-red-600">{tValidation('lastNameRequired')}</p>
              )}
            </div>
          </div>

          {/* Company */}
          <div className="space-y-2">
            <Label htmlFor="company">{tForm('company')}</Label>
            <Input
              id="company"
              {...register('company')}
              placeholder={tForm('companyPlaceholder')}
              disabled={isSubmitting || isLoading}
            />
          </div>

          {/* Street Address */}
          <div className="space-y-2">
            <Label htmlFor="street_address">{tForm('streetAddress')} *</Label>
            <Input
              id="street_address"
              {...register('street_address')}
              placeholder={tForm('streetAddressPlaceholder')}
              disabled={isSubmitting || isLoading}
            />
            {errors.street_address && (
              <p className="text-sm text-red-600">{tValidation('streetAddressRequired')}</p>
            )}
          </div>

          {/* City and Postal Code */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="postal_code">{tForm('postalCode')} *</Label>
              <Input
                id="postal_code"
                {...register('postal_code')}
                placeholder={tForm('postalCodePlaceholder')}
                disabled={isSubmitting || isLoading}
              />
              {errors.postal_code && (
                <p className="text-sm text-red-600">{tValidation('postalCodeRequired')}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="city">{tForm('city')} *</Label>
              <Input
                id="city"
                {...register('city')}
                placeholder={tForm('cityPlaceholder')}
                disabled={isSubmitting || isLoading}
              />
              {errors.city && (
                <p className="text-sm text-red-600">{tValidation('cityRequired')}</p>
              )}
            </div>
          </div>

          {/* Country */}
          <div className="space-y-2">
            <Label htmlFor="country">{tForm('country')} *</Label>
            <Select
              value={watchedCountry}
              onValueChange={(value) => setValue('country', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {countries.map((country) => (
                  <SelectItem key={country.code} value={country.code}>
                    {country.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.country && (
              <p className="text-sm text-red-600">{errors.country.message}</p>
            )}
          </div>

          {/* Default Address Checkbox */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_default"
              checked={watchedIsDefault}
              onCheckedChange={(checked) => setValue('is_default', !!checked)}
              disabled={isSubmitting || isLoading}
            />
            <Label htmlFor="is_default" className="text-sm">
              {tForm('setAsDefault')}
            </Label>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="flex-1"
            >
              {isSubmitting || isLoading ? tForm('saving') : tForm('save')}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || isLoading}
              className="flex-1"
            >
              {tForm('cancel')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
