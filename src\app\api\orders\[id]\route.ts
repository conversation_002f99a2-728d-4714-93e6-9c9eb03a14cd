import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createClient as createAdminClient } from '@supabase/supabase-js'
import { sendShippingNotificationEmail } from '@/lib/email'

// Create admin client for bypassing RLS
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  console.log(`🔍 API: Fetching order with ID: ${id}`)

  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    console.log(`🔍 API: Current user:`, user ? { id: user.id, email: user.email } : 'No user')

    // Determine if the id is a UUID or an order number
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)

    // Build query based on whether it's a UUID or order number
    let query = supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          products (
            id,
            title,
            price,
            discount_price
          )
        )
      `)

    if (isUUID) {
      query = query.eq('id', id)
    } else {
      query = query.eq('order_number', id)
    }

    const { data: order, error } = await query.single()

    if (error) {
      console.error('🔍 API: Error fetching order:', error)
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    console.log(`🔍 API: Order found:`, {
      id: order.id,
      user_id: order.user_id,
      email: order.email,
      status: order.status,
      payment_status: order.payment_status
    })

    // Check if user has access to this order
    // For orders with user_id: only the owner can access
    // For guest orders (user_id is null): anyone can access (they need the order ID)
    if (order.user_id && user?.id !== order.user_id) {
      console.log(`🔍 API: Access denied - Order belongs to user ${order.user_id}, current user is ${user?.id}`)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      )
    }

    console.log(`🔍 API: Access granted - returning order`)
    return NextResponse.json(order)

  } catch (error) {
    console.error('Error in orders API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const updates = await request.json()

    // Get the current order first to check for status changes
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)

    let currentOrderQuery = supabaseAdmin
      .from('orders')
      .select('*')

    if (isUUID) {
      currentOrderQuery = currentOrderQuery.eq('id', id)
    } else {
      currentOrderQuery = currentOrderQuery.eq('order_number', id)
    }

    const { data: currentOrder, error: currentOrderError } = await currentOrderQuery.single()

    if (currentOrderError) {
      console.error('Error fetching current order:', currentOrderError)
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    // Update order based on whether it's a UUID or order number
    let updateQuery = supabase
      .from('orders')
      .update(updates)

    if (isUUID) {
      updateQuery = updateQuery.eq('id', id)
    } else {
      updateQuery = updateQuery.eq('order_number', id)
    }

    const { data: order, error } = await updateQuery
      .select()
      .single()

    if (error) {
      console.error('Error updating order:', error)
      return NextResponse.json(
        { error: 'Failed to update order' },
        { status: 500 }
      )
    }

    // Check if status changed to 'shipped' and tracking number is provided
    const statusChangedToShipped = updates.status === 'shipped' && currentOrder.status !== 'shipped'
    const hasTrackingNumber = updates.tracking_number || order.tracking_number

    if (statusChangedToShipped && hasTrackingNumber) {
      try {
        console.log('📧 Order API: Sending shipping notification email for order:', order.id)

        // Get site settings to determine default language
        let locale = 'de'; // Default fallback
        try {
          const { data: settings } = await supabaseAdmin
            .from('site_settings')
            .select('default_language')
            .single();

          if (settings?.default_language) {
            locale = settings.default_language;
          }
        } catch {
          console.log('📧 Order API: Could not fetch site settings, using default locale:', locale);
        }

        await sendShippingNotificationEmail(order, hasTrackingNumber, locale)
        console.log('📧 Order API: Shipping notification email sent successfully')
      } catch (emailError) {
        console.error('📧 Order API: Error sending shipping notification email:', emailError)
        // Don't fail the order update if email fails, but log the error
      }
    }

    return NextResponse.json(order)

  } catch (error) {
    console.error('Error in orders API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
