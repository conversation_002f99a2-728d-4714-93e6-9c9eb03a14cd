import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { code, orderAmount } = await request.json()
    
    if (!code || !orderAmount) {
      return NextResponse.json(
        { error: 'Coupon code and order amount are required' },
        { status: 400 }
      )
    }

    const supabase = await createClient()
    
    // Get coupon by code
    const { data: coupon, error: couponError } = await supabase
      .from('coupons')
      .select('*')
      .eq('code', code.toUpperCase())
      .eq('is_active', true)
      .single()

    if (couponError || !coupon) {
      return NextResponse.json(
        { error: 'Invalid coupon code' },
        { status: 404 }
      )
    }

    // Check if coupon is still valid (date)
    const now = new Date()
    const validFrom = new Date(coupon.valid_from)
    const validUntil = new Date(coupon.valid_until)

    if (now < validFrom || now > validUntil) {
      return NextResponse.json(
        { error: 'Coupon has expired or is not yet valid' },
        { status: 400 }
      )
    }

    // Check usage limit
    if (coupon.usage_limit && coupon.used_count >= coupon.usage_limit) {
      return NextResponse.json(
        { error: 'Coupon usage limit exceeded' },
        { status: 400 }
      )
    }

    // Check minimum order amount
    if (coupon.minimum_order_amount && orderAmount < coupon.minimum_order_amount) {
      return NextResponse.json(
        { 
          error: `Minimum order amount of CHF ${coupon.minimum_order_amount} required`,
          minimumAmount: coupon.minimum_order_amount
        },
        { status: 400 }
      )
    }

    // Calculate discount
    let discountAmount = 0
    if (coupon.type === 'percentage') {
      discountAmount = (orderAmount * coupon.value) / 100
    } else if (coupon.type === 'fixed_amount') {
      discountAmount = coupon.value
    }

    // Ensure discount doesn't exceed order amount
    discountAmount = Math.min(discountAmount, orderAmount)

    return NextResponse.json({
      success: true,
      coupon: {
        id: coupon.id,
        code: coupon.code,
        type: coupon.type,
        value: coupon.value,
        discountAmount: discountAmount
      },
      discountAmount,
      newTotal: orderAmount - discountAmount
    })

  } catch (error) {
    console.error('Error validating coupon:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
