import { metadata } from '../app/layout'

// Test the metadata export
describe('Layout Metadata', () => {
  it('should have correct title and description', () => {
    expect(metadata.title).toBe('PrimeCaffe - Premium Kaffee aus der Schweiz')
    expect(metadata.description).toBe('Entdecken Sie erstklassigen Kaffeegenuss mit PrimeCaffe. Premium Kaffeekapseln, Bohnen und Zubehör direkt aus der Schweiz.')
  })
})

// Test component structure
describe('Layout Component', () => {
  it('should export a default function', () => {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const LayoutModule = require('../app/layout')
    expect(typeof LayoutModule.default).toBe('function')
  })
})
