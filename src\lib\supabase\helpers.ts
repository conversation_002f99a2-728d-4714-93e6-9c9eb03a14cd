import { AuthApiError, createClient as createAdminClient, type SupabaseClient, type User } from '@supabase/supabase-js'

/**
 * Safely retrieve the current user. If the refresh token is invalid
 * the user will be signed out to clear stale credentials.
 * Handles guest users gracefully without throwing errors.
 */
export async function safeGetUser(
  supabase: SupabaseClient
): Promise<User | null> {
  try {
    const { data, error } = await supabase.auth.getUser()
    if (error) {
      // Handle common guest user scenarios gracefully
      if (error.message.includes('Auth session missing') ||
          error.message.includes('Invalid Refresh Token') ||
          error.message.includes('refresh_token_not_found')) {
        // This is normal for guest users - don't log as error
        return null
      }
      throw error
    }
    return data.user ?? null
  } catch (err) {
    if (err instanceof AuthApiError) {
      if (err.message.includes('Invalid Refresh Token') ||
          err.message.includes('refresh_token_not_found')) {
        // Clear invalid session silently for guest users
        try {
          await supabase.auth.signOut()
        } catch {
          // Ignore sign out errors for guest users
        }
        return null
      }
      if (err.message.includes('Auth session missing')) {
        // Normal for guest users - return null without error
        return null
      }
    }
    // Only log unexpected errors
    console.error('safeGetUser unexpected error:', err)
    return null
  }
}

/**
 * Ensure the specified storage bucket exists. Uses the service role key
 * if available to create the bucket when missing. Errors are logged
 * but not thrown to keep runtime robust.
 */
export async function ensureBucketExists(bucketName: string) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !serviceKey) return

  const admin = createAdminClient(supabaseUrl, serviceKey, {
    auth: { autoRefreshToken: false, persistSession: false },
  })

  const { data: buckets, error: listError } = await admin.storage.listBuckets()
  if (listError) {
    console.error('ensureBucketExists list error:', listError)
    return
  }

  const exists = buckets?.some((b) => b.name === bucketName)
  if (!exists) {
    const { error: createError } = await admin.storage.createBucket(bucketName, {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
      fileSizeLimit: 5 * 1024 * 1024,
    })

    if (createError) {
      console.error('ensureBucketExists create error:', createError)
    }
  }
}
