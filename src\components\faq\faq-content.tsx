'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, Search, Coffee, Package, CreditCard, Truck, RotateCcw, Settings } from 'lucide-react';

const categoryIcons = {
  all: null,
  products: Coffee,
  orders: Package,
  payment: CreditCard,
  shipping: Truck,
  returns: RotateCcw,
  account: Settings
};

export function FAQContent() {
  const t = useTranslations('faq');
  const locale = useLocale();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [openItems, setOpenItems] = useState<number[]>([]);

  // Get FAQ items from translations
  const faqItems = t.raw('items') as Array<{
    category: string;
    question: string;
    answer: string;
  }>;

  // Get categories from translations
  const categories = Object.keys(t.raw('categories')).map(key => ({
    id: key,
    name: t(`categories.${key}`),
    icon: categoryIcons[key as keyof typeof categoryIcons]
  }));

  const filteredFAQs = faqItems.filter((faq) => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  }).map((faq, index) => ({ ...faq, id: index + 1 }));

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <>
      {/* Search */}
      <div className="max-w-md mx-auto mb-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={t('searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 justify-center mb-12">
        {categories.map((category) => {
          const IconComponent = category.icon;
          return (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="flex items-center gap-2"
            >
              {IconComponent && <IconComponent className="h-4 w-4" />}
              {category.name}
            </Button>
          );
        })}
      </div>

      {/* FAQ List */}
      <div className="max-w-4xl mx-auto space-y-4 mb-16">
        {filteredFAQs.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t('noResults')}</h3>
              <p className="text-muted-foreground">
                {t('noResultsDescription')}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredFAQs.map((faq) => (
            <Collapsible
              key={faq.id}
              open={openItems.includes(faq.id)}
              onOpenChange={() => toggleItem(faq.id)}
            >
              <Card>
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Badge variant="secondary">
                          {categories.find(cat => cat.id === faq.category)?.name}
                        </Badge>
                        <CardTitle className="text-left text-lg">
                          {faq.question}
                        </CardTitle>
                      </div>
                      <ChevronDown 
                        className={`h-5 w-5 transition-transform ${
                          openItems.includes(faq.id) ? 'rotate-180' : ''
                        }`} 
                      />
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <CardContent className="pt-0">
                    <p className="text-muted-foreground leading-relaxed">
                      {faq.answer}
                    </p>
                  </CardContent>
                </CollapsibleContent>
              </Card>
            </Collapsible>
          ))
        )}
      </div>

      {/* Contact CTA */}
      <Card className="bg-muted/50">
        <CardContent className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">{t('contactTitle')}</h2>
          <p className="text-muted-foreground mb-6">
            {t('contactDescription')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link href={`/${locale}/contact`}>{t('contactButton')}</Link>
            </Button>
            <Button variant="outline" asChild>
              <a href="mailto:<EMAIL>">{t('emailButton')}</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
