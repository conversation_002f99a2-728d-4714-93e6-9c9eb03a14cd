import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { calculateVATByCategory } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json()
    
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Accesso admin richiesto' },
        { status: 403 }
      )
    }

    // Validate required fields
    if (!orderData.email || !orderData.customer_info || !orderData.shipping_address || !orderData.items || orderData.items.length === 0) {
      return NextResponse.json(
        { error: 'Dati ordine incompleti' },
        { status: 400 }
      )
    }

    // Check if user exists with this email
    let finalUser = null
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('id, email')
      .eq('email', orderData.email.toLowerCase())
      .single()

    if (existingUser) {
      finalUser = existingUser
    }

    // Validate products and calculate totals
    let calculatedSubtotal = 0
    const validatedItems = []

    for (const item of orderData.items) {
      const { data: product, error: productError } = await supabaseAdmin
        .from('products')
        .select('id, title, price, discount_price, is_available, category')
        .eq('id', item.product_id)
        .single()

      if (productError || !product) {
        return NextResponse.json(
          { error: `Prodotto non trovato: ${item.product_id}` },
          { status: 400 }
        )
      }

      if (!product.is_available) {
        return NextResponse.json(
          { error: `Prodotto non disponibile: ${product.title}` },
          { status: 400 }
        )
      }

      const unitPrice = product.discount_price || product.price
      const totalPrice = unitPrice * item.quantity

      validatedItems.push({
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: unitPrice,
        total_price: totalPrice,
        category: product.category
      })

      calculatedSubtotal += totalPrice
    }

    // Get site settings for VAT calculation
    const { data: siteSettings } = await supabaseAdmin
      .from('site_settings')
      .select('use_category_vat, vat_rate, vat_rate_coffee, vat_rate_accessories')
      .maybeSingle()

    const vatSettings = siteSettings || {
      use_category_vat: false,
      vat_rate: 0.077,
      vat_rate_coffee: 0.077,
      vat_rate_accessories: 0.077
    }

    // Calculate shipping and tax using category-based VAT
    const shippingCost = calculatedSubtotal >= 90 ? 0 : 9.90

    // Calculate VAT breakdown by category
    const vatBreakdown = calculateVATByCategory(
      validatedItems.map(item => ({
        quantity: item.quantity,
        products: {
          price: item.unit_price,
          discount_price: undefined,
          category: item.category
        }
      })),
      vatSettings
    )

    const taxAmount = vatBreakdown.totalVAT
    const totalAmount = calculatedSubtotal + shippingCost + taxAmount

    console.log('🛒 Creating manual order...')

    // Create order
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .insert({
        user_id: finalUser?.id || null,
        email: orderData.email,
        status: 'confirmed', // Manual orders start as confirmed
        subtotal: calculatedSubtotal,
        shipping_cost: shippingCost,
        tax_amount: taxAmount,
        discount_amount: 0,
        total_amount: totalAmount,
        currency: 'CHF',
        payment_status: 'paid', // Manual orders are considered paid
        payment_intent_id: null, // No Stripe payment
        shipping_address: orderData.shipping_address,
        billing_address: orderData.billing_address,
        notes: orderData.notes || null
      })
      .select()
      .single()

    if (orderError) {
      console.error('❌ Error creating order:', orderError)
      return NextResponse.json(
        { error: 'Errore nella creazione dell\'ordine' },
        { status: 500 }
      )
    }

    console.log('✅ Order created:', order.id)

    // Create order items
    const orderItemsData = validatedItems.map(item => ({
      order_id: order.id,
      product_id: item.product_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.total_price
    }))

    const { error: itemsError } = await supabaseAdmin
      .from('order_items')
      .insert(orderItemsData)

    if (itemsError) {
      console.error('❌ Error creating order items:', itemsError)
      // Try to clean up the order
      await supabaseAdmin
        .from('orders')
        .delete()
        .eq('id', order.id)
      
      return NextResponse.json(
        { error: 'Errore nella creazione degli articoli dell\'ordine' },
        { status: 500 }
      )
    }

    console.log('✅ Order items created')

    // Update product inventory
    for (const item of validatedItems) {
      // Get current inventory
      const { data: product } = await supabaseAdmin
        .from('products')
        .select('inventory_count')
        .eq('id', item.product_id)
        .single()

      if (product) {
        await supabaseAdmin
          .from('products')
          .update({
            inventory_count: Math.max(0, product.inventory_count - item.quantity)
          })
          .eq('id', item.product_id)
      }
    }

    console.log('✅ Inventory updated')

    // If user exists, award points using the new gamification system
    if (finalUser) {
      try {
        const { updateUserGamification } = await import('@/lib/gamification')
        const { pointsEarned, newLevel } = await updateUserGamification(
          finalUser.id,
          totalAmount,
          order.id
        )
        console.log(`✅ Points awarded: ${pointsEarned} points to user ${finalUser.id}`)
        if (newLevel) {
          console.log(`✅ User ${finalUser.id} reached new level: ${newLevel.name}`)
        }
      } catch (pointsError) {
        console.error('❌ Error awarding points:', pointsError)
        // Don't fail the order creation for points errors
      }
    }

    return NextResponse.json({
      success: true,
      order: {
        id: order.id,
        order_number: order.order_number,
        total_amount: order.total_amount,
        status: order.status,
        payment_status: order.payment_status
      }
    })

  } catch (error) {
    console.error('❌ Error in manual order creation:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}
