-- Migration: Add Advanced Level Management System
-- Date: 2025-06-25
-- Description: Adds level_management_config table for advanced level generation and management

-- Create level management configuration table
CREATE TABLE IF NOT EXISTS level_management_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    total_levels INTEGER NOT NULL DEFAULT 4,
    multiplier_mode VARCHAR(20) NOT NULL DEFAULT 'uniform' CHECK (multiplier_mode IN ('uniform', 'incremental')),
    uniform_multiplier DECIMAL(3,2) DEFAULT 1.0,
    incremental_multiplier_start DECIMAL(3,2) DEFAULT 1.0,
    incremental_multiplier_step DECIMAL(3,2) DEFAULT 0.1,
    point_progression_type VARCHAR(20) NOT NULL DEFAULT 'linear' CHECK (point_progression_type IN ('linear', 'exponential', 'custom')),
    base_point_increment INTEGER DEFAULT 200,
    exponential_factor DECIMAL(3,2) DEFAULT 1.5,
    custom_progression_formula TEXT,
    starting_points INTEGER DEFAULT 0,
    discount_progression_type VARCHAR(20) NOT NULL DEFAULT 'linear' CHECK (discount_progression_type IN ('linear', 'exponential', 'custom')),
    base_discount_increment DECIMAL(3,2) DEFAULT 2.5,
    max_discount_percentage DECIMAL(5,2) DEFAULT 20.0,
    level_naming_pattern VARCHAR(50) DEFAULT 'Level {level}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert default configuration
INSERT INTO level_management_config (
    total_levels,
    multiplier_mode,
    uniform_multiplier,
    point_progression_type,
    base_point_increment,
    starting_points,
    discount_progression_type,
    base_discount_increment,
    max_discount_percentage,
    level_naming_pattern
) VALUES (
    4,
    'incremental',
    1.0,
    'linear',
    200,
    0,
    'linear',
    5.0,
    20.0,
    'Level {level}'
) ON CONFLICT DO NOTHING;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_level_management_config_active ON level_management_config(is_active);

-- Add RLS policies
ALTER TABLE level_management_config ENABLE ROW LEVEL SECURITY;

-- Policy: Only admins can view level management config
CREATE POLICY "Admin can view level management config" ON level_management_config
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );

-- Policy: Only admins can modify level management config
CREATE POLICY "Admin can modify level management config" ON level_management_config
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );
