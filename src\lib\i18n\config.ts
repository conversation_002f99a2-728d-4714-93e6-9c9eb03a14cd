export const locales = ['de', 'fr', 'it'] as const;
export type Locale = (typeof locales)[number];

export const defaultLocale: Locale = 'de';

export const localeNames: Record<Locale, string> = {
  de: 'Deutsch',
  fr: 'Français', 
  it: 'Italiano'
};

export const localeFlags: Record<Locale, string> = {
  de: '🇩🇪',
  fr: '🇫🇷',
  it: '🇮🇹'
};

// Currency formatting for Swiss market (CHF for all locales)
export const currencyConfig = {
  currency: 'CHF',
  locale: 'de-CH' // Swiss German formatting for all languages
};

// Date formatting for Swiss market (dd/mm/yyyy)
export const dateConfig = {
  locale: 'de-CH',
  format: 'dd/MM/yyyy'
};
