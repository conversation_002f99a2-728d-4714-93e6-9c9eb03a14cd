'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Gift, Star, Trophy, Package } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Image from 'next/image';

interface Product {
  id: string;
  title: string;
  images: string[];
  price: number;
}

interface GiftReward {
  id: string;
  type: 'level' | 'league';
  trigger_value: number;
  option_1_product_id: string | null;
  option_2_product_id: string | null;
  option_3_product_id: string | null;
  option_1_product?: Product;
  option_2_product?: Product;
  option_3_product?: Product;
}

interface PendingGift {
  id: string;
  claimed_at: string;
  status: 'pending' | 'selected' | 'fulfilled';
  gift_rewards: GiftReward;
}

export default function GiftHistory() {
  const [pendingGifts, setPendingGifts] = useState<PendingGift[]>([]);
  const [loading, setLoading] = useState(true);
  const [selecting, setSelecting] = useState<string | null>(null);
  const t = useTranslations('account');
  const { toast } = useToast();

  useEffect(() => {
    fetchPendingGifts();
  }, []);

  const fetchPendingGifts = async () => {
    try {
      const response = await fetch('/api/account/gift-claims');
      if (response.ok) {
        const data = await response.json();
        setPendingGifts(data.claims || []);
      }
    } catch (error) {
      console.error('Error fetching pending gifts:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectGift = async (giftId: string, productId: string) => {
    setSelecting(giftId);
    try {
      const response = await fetch('/api/gifts/select', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ claimId: giftId, productId })
      });

      if (response.ok) {
        toast({ title: 'Regalo selezionato!', description: 'Il tuo regalo è stato aggiunto al prossimo ordine.' });
        fetchPendingGifts(); // Refresh the list
      } else {
        throw new Error('Failed to select gift');
      }
    } catch (error) {
      console.error('Error selecting gift:', error);
      toast({ title: 'Errore', description: 'Impossibile selezionare il regalo.', variant: 'destructive' });
    } finally {
      setSelecting(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {t('gifts.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (pendingGifts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {t('gifts.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Nessun regalo da riscattare al momento.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Gift className="h-5 w-5" />
          {t('gifts.title')}
        </CardTitle>
        <p className="text-muted-foreground">
          Scegli il tuo regalo tra le opzioni disponibili!
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {pendingGifts.map((gift) => (
            <Card key={gift.id} className="border-l-4 border-l-green-500">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-green-100 p-2 rounded-full">
                    {gift.gift_rewards.type === 'league' ? (
                      <Trophy className="h-5 w-5 text-green-600" />
                    ) : (
                      <Star className="h-5 w-5 text-green-600" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-semibold">
                      {gift.gift_rewards.type === 'league'
                        ? `🏆 Regalo Lega ${gift.gift_rewards.trigger_value}`
                        : `⭐ Regalo Livello ${gift.gift_rewards.trigger_value}`
                      }
                    </h4>
                    <Badge variant="secondary" className="bg-green-50 text-green-700">
                      Da riscattare
                    </Badge>
                  </div>
                </div>

                <div className="space-y-3">
                  <p className="text-sm font-medium flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Scegli il tuo regalo:
                  </p>

                  <div className="grid gap-3 md:grid-cols-3">
                    {[
                      { product: gift.gift_rewards.option_1_product, id: gift.gift_rewards.option_1_product_id },
                      { product: gift.gift_rewards.option_2_product, id: gift.gift_rewards.option_2_product_id },
                      { product: gift.gift_rewards.option_3_product, id: gift.gift_rewards.option_3_product_id }
                    ].filter(option => option.product && option.id).map((option, index) => (
                      <div key={index} className="border rounded-lg p-3 hover:bg-gray-50 transition-colors">
                        <div className="flex items-center gap-3 mb-3">
                          {option.product!.images?.[0] && (
                            <Image
                              src={option.product!.images[0]}
                              alt={option.product!.title}
                              width={48}
                              height={48}
                              className="rounded object-cover"
                            />
                          )}
                          <div className="flex-1">
                            <p className="font-medium text-sm">{option.product!.title}</p>
                            <p className="text-xs text-muted-foreground">
                              CHF {option.product!.price.toFixed(2)}
                            </p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          className="w-full"
                          onClick={() => selectGift(gift.id, option.id!)}
                          disabled={selecting === gift.id}
                        >
                          {selecting === gift.id ? 'Selezionando...' : 'Scegli questo'}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
