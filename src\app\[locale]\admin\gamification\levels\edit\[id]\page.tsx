'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import LevelForm from '@/components/admin/level-form'

interface UserLevel {
  id: string
  level: number
  name: string
  minimum_points: number
  discount_percentage: number
  points_multiplier: number
}

interface EditLevelPageProps {
  params: Promise<{ locale: string; id: string }>
}

export default function EditLevelPage({ params }: EditLevelPageProps) {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = createClient()

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [level, setLevel] = useState<UserLevel | null>(null)
  const [levelId, setLevelId] = useState('')

  useEffect(() => {
    const getParams = async () => {
      const resolved = await params
      setLevelId(resolved.id)
    }
    getParams()
  }, [params])

  useEffect(() => {
    const checkAuthAndLoad = async () => {
      if (!levelId) return
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          router.push(`/${locale}/login`)
          return
        }
        const { data: profile } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()
        if (!profile?.is_admin) {
          router.push(`/${locale}`)
          return
        }
        setAuthChecked(true)
        const { data: levelData, error } = await supabase
          .from('user_levels')
          .select('*')
          .eq('id', levelId)
          .single()
        if (error || !levelData) {
          router.push(`/${locale}/admin/gamification`)
          return
        }
        setLevel(levelData)
        setLoading(false)
      } catch {
        router.push(`/${locale}`)
      }
    }
    if (!authChecked && levelId) {
      checkAuthAndLoad()
    }
  }, [authChecked, levelId, locale, router, supabase])

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!level) {
    return null
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/admin/gamification`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t('gamificationPage.newLevel')}</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('gamificationPage.newLevel')}</CardTitle>
        </CardHeader>
        <CardContent>
          <LevelForm locale={locale} level={level} />
        </CardContent>
      </Card>
    </div>
  )
}

