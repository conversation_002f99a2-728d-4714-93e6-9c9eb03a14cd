'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Coffee, RefreshCw, Home, AlertTriangle, Wrench } from 'lucide-react'
import Link from 'next/link'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  // Use hardcoded German text since this is an error boundary that might be rendered
  // outside of the i18n context when the i18n system itself has failed
  const locale = 'de' // fallback locale

  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error)
  }, [error])

  return (
    <div className="min-h-[80vh] flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        <Card className="border-2 border-red-200 bg-red-50/50">
          <CardContent className="p-12">
            {/* Error Animation */}
            <div className="relative mb-8">
              <div className="flex justify-center items-center space-x-4">
                <Coffee className="h-20 w-20 text-red-500 animate-pulse" />
                <div className="relative">
                  <AlertTriangle className="h-16 w-16 text-red-500" />
                  <div className="absolute -top-1 -right-1">
                    <div className="w-3 h-3 bg-red-400 rounded-full animate-ping"></div>
                  </div>
                </div>
              </div>

              {/* Steam effect */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="flex space-x-2">
                  <div className="w-1 h-8 bg-gradient-to-t from-red-300 to-transparent rounded-full animate-pulse"></div>
                  <div className="w-1 h-6 bg-gradient-to-t from-red-300 to-transparent rounded-full animate-pulse delay-75"></div>
                  <div className="w-1 h-10 bg-gradient-to-t from-red-300 to-transparent rounded-full animate-pulse delay-150"></div>
                </div>
              </div>
            </div>

            {/* Error Message */}
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Ups! Die Kaffeemaschine ist kaputt! ☕💥
            </h1>
            <p className="text-xl text-red-600 font-semibold mb-6">
              Etwas ist schiefgelaufen...
            </p>

            {/* Error Details */}
            <div className="bg-white rounded-lg p-6 mb-8 border border-red-200">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <Wrench className="h-5 w-5 text-red-500" />
                <span className="font-semibold text-gray-700">Was ist passiert?</span>
              </div>
              <p className="text-gray-600 mb-4">
                Unsere Kaffeemaschine (auch bekannt als Server) hat einen kleinen Schluckauf. Das passiert manchmal, auch den besten Baristas!
              </p>

              {/* Technical details for development */}
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4 text-left">
                  <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                    Technische Details (nur für Entwickler)
                  </summary>
                  <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono text-gray-700 overflow-auto">
                    <p><strong>Error:</strong> {error.message}</p>
                    {error.digest && <p><strong>Digest:</strong> {error.digest}</p>}
                    {error.stack && (
                      <details className="mt-2">
                        <summary className="cursor-pointer">Stack Trace</summary>
                        <pre className="mt-1 whitespace-pre-wrap">{error.stack}</pre>
                      </details>
                    )}
                  </div>
                </details>
              )}
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  onClick={reset}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <RefreshCw className="mr-2 h-5 w-5" />
                  Nochmal versuchen
                </Button>

                <Button
                  size="lg"
                  variant="outline"
                  className="border-amber-600 text-amber-600 hover:bg-amber-50"
                  asChild
                >
                  <Link href={`/${locale}`}>
                    <Home className="mr-2 h-5 w-5" />
                    Zur Startseite
                  </Link>
                </Button>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  className="border-gray-300"
                  asChild
                >
                  <Link href={`/${locale}/coffee-box-builder`}>
                    <Coffee className="mr-2 h-4 w-4" />
                    Coffee Box Builder
                  </Link>
                </Button>

                <Button
                  variant="outline"
                  className="border-gray-300"
                  asChild
                >
                  <Link href={`/${locale}/shop`}>
                    Shop besuchen
                  </Link>
                </Button>
              </div>
            </div>

            {/* Fun Footer */}
            <div className="mt-8 pt-6 border-t border-red-200">
              <p className="text-sm text-gray-500">
                Keine Sorge – wir reparieren das schneller als Sie &ldquo;Espresso&rdquo; sagen können! ⚡
              </p>
              <p className="text-xs text-gray-400 mt-2">
                Fehler-ID: {error.digest || 'COFFEE_MACHINE_ERROR'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

