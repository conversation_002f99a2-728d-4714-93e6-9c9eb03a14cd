-- Migration: Change user levels from minimum_spend to minimum_points
-- Date: 2025-06-25

-- Add minimum_points column to user_levels table
ALTER TABLE user_levels 
ADD COLUMN IF NOT EXISTS minimum_points INTEGER DEFAULT 0;

-- Update existing levels to use points instead of spend
-- Assuming 1 CHF = 1 point (can be adjusted based on points_per_chf setting)
UPDATE user_levels 
SET minimum_points = CAST(minimum_spend AS INTEGER)
WHERE minimum_points = 0;

-- Drop the old minimum_spend column
ALTER TABLE user_levels 
DROP COLUMN IF EXISTS minimum_spend;

-- Update any existing user levels to be consistent
-- Bronze: 0 points
-- Silver: 200 points  
-- Gold: 500 points
-- Platinum: 1000 points

-- Clear existing levels and insert new point-based levels
DELETE FROM user_levels;

INSERT INTO user_levels (level, name, minimum_points, discount_percentage, points_multiplier) VALUES
(1, 'Bronze', 0, 0, 1.0),
(2, 'Silver', 200, 5, 1.2),
(3, 'Gold', 500, 10, 1.5),
(4, 'Platinum', 1000, 15, 2.0);
