'use client'

import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { formatCurrency } from '@/lib/utils'
import { Search, Plus, Package, ToggleLeft, ToggleRight, Edit } from 'lucide-react'
import Link from 'next/link'
import BundleActions from '@/components/admin/bundle-actions'
import { useTranslations } from 'next-intl'
import { AdminBackButton } from './admin-back-button'

interface Bundle {
  id: string
  title: string
  description: string
  slug: string
  image: string
  total_price: number
  discount_price: number
  is_available: boolean
}

interface BundleManagementContentProps {
  bundles: Bundle[]
  locale: string
}

export default function BundleManagementContent({ bundles, locale }: BundleManagementContentProps) {
  const t = useTranslations('admin.bundleManagement')

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/admin/bundles/create`}>
            <Plus className="mr-2 h-4 w-4" />
            {t('createNew')}
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('searchPlaceholder')}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bundle Statistics */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('totalBundles')}</p>
                <p className="text-2xl font-bold">{bundles?.length || 0}</p>
              </div>
              <Package className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('available')}</p>
                <p className="text-2xl font-bold">
                  {bundles?.filter(bundle => bundle.is_available).length || 0}
                </p>
              </div>
              <ToggleRight className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('unavailable')}</p>
                <p className="text-2xl font-bold">
                  {bundles?.filter(bundle => !bundle.is_available).length || 0}
                </p>
              </div>
              <ToggleLeft className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('averageSavings')}</p>
                <p className="text-2xl font-bold">
                  {bundles && bundles.length > 0 
                    ? `${Math.round(bundles.reduce((sum, bundle) => {
                        const savings = bundle.total_price - bundle.discount_price;
                        const percentage = (savings / bundle.total_price) * 100;
                        return sum + percentage;
                      }, 0) / bundles.length)}%`
                    : '0%'
                  }
                </p>
              </div>
              <Package className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bundles Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('allBundles')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Bundle</th>
                  <th className="text-left py-3 px-4">{t('originalPrice')}</th>
                  <th className="text-left py-3 px-4">{t('bundlePrice')}</th>
                  <th className="text-left py-3 px-4">{t('savings')}</th>
                  <th className="text-left py-3 px-4">{t('status')}</th>
                  <th className="text-left py-3 px-4">{t('actions')}</th>
                </tr>
              </thead>
              <tbody>
                {bundles?.map((bundle) => {
                  const savings = bundle.total_price - bundle.discount_price;
                  const savingsPercentage = (savings / bundle.total_price) * 100;
                  
                  return (
                    <tr key={bundle.id} className="border-b hover:bg-muted/50">
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-3">
                          {bundle.image && (
                            <div
                              className="w-10 h-10 rounded bg-cover bg-center"
                              style={{ backgroundImage: `url(${bundle.image})` }}
                            />
                          )}
                          <div>
                            <div className="font-medium">{bundle.title}</div>
                            <div className="text-sm text-muted-foreground">
                              {bundle.description?.substring(0, 50)}...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-semibold text-muted-foreground line-through">
                          {formatCurrency(bundle.total_price)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-semibold text-green-600">
                          {formatCurrency(bundle.discount_price)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          <div className="font-semibold text-green-600">
                            {formatCurrency(savings)}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ({savingsPercentage.toFixed(1)}%)
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge variant={bundle.is_available ? "default" : "secondary"}>
                          {bundle.is_available ? t('available') : t('unavailable')}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/${locale}/admin/bundles/edit/${bundle.id}`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <BundleActions bundle={bundle} locale={locale} />
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
            {(!bundles || bundles.length === 0) && (
              <div className="text-center py-8 text-muted-foreground">
                {t('noBundles')}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
