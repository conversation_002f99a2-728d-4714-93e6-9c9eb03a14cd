'use client'

import { useState, useEffect, useMemo, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, calculateVATByCategory } from '@/lib/utils'
import { AdminBackButton } from '@/components/admin/admin-back-button'
import { Plus, Minus, Trash2, User } from 'lucide-react'
import type { Product, User as UserType, UserAddress } from '@/types'

interface OrderItem {
  product_id: string
  product: Product
  quantity: number
  unit_price: number
  total_price: number
}

interface CustomerSearchResult extends UserType {
  user_addresses: UserAddress[]
}

interface OrderFormData {
  email: string
  firstName: string
  lastName: string
  phone: string
  company: string
  street: string
  city: string
  postalCode: string
  country: string
  notes: string
  items: OrderItem[]
}

export default function CreateOrderPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const { toast } = useToast()
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [products, setProducts] = useState<Product[]>([])
  const [creating, setCreating] = useState(false)
  const [formData, setFormData] = useState<OrderFormData>({
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    company: '',
    street: '',
    city: '',
    postalCode: '',
    country: 'CH',
    notes: '',
    items: []
  })

  // Customer search states
  const [customerSearch, setCustomerSearch] = useState('')
  const [customerResults, setCustomerResults] = useState<CustomerSearchResult[]>([])
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false)
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null)
  const customerSearchRef = useRef<HTMLDivElement>(null)

  // VAT settings state
  const [vatSettings, setVatSettings] = useState({
    use_category_vat: false,
    vat_rate: 0.077,
    vat_rate_coffee: 0.077,
    vat_rate_accessories: 0.077
  })

  // Check admin auth
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          router.push(`/${locale}/admin/login`)
          return
        }

        const { data: profile } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (!profile?.is_admin) {
          router.push(`/${locale}`)
          return
        }

        setAuthChecked(true)
      } catch (error) {
        console.error('Auth check error:', error)
        router.push(`/${locale}/admin/login`)
      }
    }

    checkAuth()
  }, [supabase, router, locale])

  // Load products
  useEffect(() => {
    const loadProducts = async () => {
      if (!authChecked) return

      try {
        const { data: productsData, error } = await supabase
          .from('products')
          .select('*')
          .eq('is_available', true)
          .order('title')

        if (error) {
          console.error('Error loading products:', error)
          toast({
            title: 'Errore',
            description: 'Errore nel caricamento dei prodotti',
            variant: 'destructive'
          })
          return
        }

        setProducts(productsData || [])

        // Load VAT settings
        const { data: siteSettings } = await supabase
          .from('site_settings')
          .select('use_category_vat, vat_rate, vat_rate_coffee, vat_rate_accessories')
          .maybeSingle()

        if (siteSettings) {
          setVatSettings({
            use_category_vat: siteSettings.use_category_vat || false,
            vat_rate: siteSettings.vat_rate || 0.077,
            vat_rate_coffee: siteSettings.vat_rate_coffee || 0.077,
            vat_rate_accessories: siteSettings.vat_rate_accessories || 0.077
          })
        }
      } catch (error) {
        console.error('Error loading products:', error)
      } finally {
        setLoading(false)
      }
    }

    loadProducts()
  }, [authChecked, supabase, toast])

  // Customer search functionality
  const searchCustomers = async (query: string) => {
    if (query.length < 2) {
      setCustomerResults([])
      setShowCustomerDropdown(false)
      return
    }

    try {
      const response = await fetch(`/api/admin/customers/search?search=${encodeURIComponent(query)}`)
      if (response.ok) {
        const data = await response.json()
        setCustomerResults(data.customers || [])
        setShowCustomerDropdown(true)
      }
    } catch (error) {
      console.error('Error searching customers:', error)
    }
  }

  const handleCustomerSearchChange = (value: string) => {
    setCustomerSearch(value)
    setFormData(prev => ({ ...prev, email: value }))

    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }

    // Set new timeout for search
    const timeout = setTimeout(() => {
      searchCustomers(value)
    }, 300)
    setSearchTimeout(timeout)
  }

  const selectCustomer = (customer: CustomerSearchResult) => {
    // Get default shipping address or first available address
    const shippingAddress = customer.user_addresses.find(addr =>
      addr.type === 'shipping' && addr.is_default
    ) || customer.user_addresses.find(addr => addr.type === 'shipping')

    setFormData(prev => ({
      ...prev,
      email: customer.email,
      firstName: shippingAddress?.first_name || customer.first_name,
      lastName: shippingAddress?.last_name || customer.last_name,
      phone: customer.phone || '',
      company: shippingAddress?.company || '',
      street: shippingAddress?.street_address || '',
      city: shippingAddress?.city || '',
      postalCode: shippingAddress?.postal_code || '',
      country: shippingAddress?.country || 'CH'
    }))

    setCustomerSearch(customer.email)
    setShowCustomerDropdown(false)
    setCustomerResults([])
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (customerSearchRef.current && !customerSearchRef.current.contains(event.target as Node)) {
        setShowCustomerDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const addProduct = (productId: string) => {
    const product = products.find(p => p.id === productId)
    if (!product) return

    const existingItem = formData.items.find(item => item.product_id === productId)
    if (existingItem) {
      updateQuantity(productId, existingItem.quantity + 1)
      return
    }

    const newItem: OrderItem = {
      product_id: productId,
      product,
      quantity: 1,
      unit_price: product.discount_price || product.price,
      total_price: product.discount_price || product.price
    }

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }))
  }

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(productId)
      return
    }

    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item => 
        item.product_id === productId 
          ? { ...item, quantity, total_price: item.unit_price * quantity }
          : item
      )
    }))
  }

  const removeItem = (productId: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.product_id !== productId)
    }))
  }

  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + item.total_price, 0)
    const shippingCost = subtotal >= 90 ? 0 : 9.90 // Free shipping over 90 CHF

    // Calculate VAT breakdown by category
    const vatBreakdown = calculateVATByCategory(
      formData.items.map(item => {
        const product = products.find(p => p.id === item.product_id)
        return {
          quantity: item.quantity,
          products: {
            price: item.unit_price,
            discount_price: undefined,
            category: (product?.category as 'coffee' | 'accessories') || 'coffee'
          }
        }
      }),
      vatSettings
    )

    const taxAmount = vatBreakdown.totalVAT
    const totalAmount = subtotal + shippingCost + taxAmount

    return { subtotal, shippingCost, taxAmount, totalAmount, vatBreakdown }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (formData.items.length === 0) {
      toast({
        title: 'Errore',
        description: 'Aggiungi almeno un prodotto all\'ordine',
        variant: 'destructive'
      })
      return
    }

    if (!formData.email || !formData.firstName || !formData.lastName || !formData.street || !formData.city || !formData.postalCode) {
      toast({
        title: 'Errore',
        description: 'Compila tutti i campi obbligatori',
        variant: 'destructive'
      })
      return
    }

    setCreating(true)

    try {
      const { subtotal, shippingCost, taxAmount, totalAmount } = calculateTotals()

      const orderData = {
        email: formData.email,
        customer_info: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          phone: formData.phone,
          email: formData.email
        },
        shipping_address: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          company: formData.company,
          street: formData.street,
          city: formData.city,
          postalCode: formData.postalCode,
          country: formData.country,
          phone: formData.phone
        },
        billing_address: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          company: formData.company,
          street: formData.street,
          city: formData.city,
          postalCode: formData.postalCode,
          country: formData.country,
          phone: formData.phone
        },
        items: formData.items.map(item => ({
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: item.unit_price
        })),
        subtotal,
        shipping_cost: shippingCost,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        notes: formData.notes,
        manual_order: true
      }

      const response = await fetch('/api/admin/orders/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Errore nella creazione dell\'ordine')
      }

      const result = await response.json()
      
      toast({
        title: 'Successo',
        description: 'Ordine creato con successo'
      })

      router.push(`/${locale}/admin/orders/${result.order.id}`)
    } catch (error) {
      console.error('Error creating order:', error)
      toast({
        title: 'Errore',
        description: error instanceof Error ? error.message : 'Errore nella creazione dell\'ordine',
        variant: 'destructive'
      })
    } finally {
      setCreating(false)
    }
  }

  if (loading || !authChecked) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">Caricamento...</div>
      </div>
    )
  }

  const { subtotal, shippingCost, taxAmount, totalAmount } = calculateTotals()

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('ordersPage.createOrder')}</h1>
          <p className="text-muted-foreground">
            Crea un nuovo ordine manuale (pagamento offline)
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>Informazioni Cliente</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="relative" ref={customerSearchRef}>
                  <Label htmlFor="email">Email Cliente *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={customerSearch}
                    onChange={(e) => handleCustomerSearchChange(e.target.value)}
                    placeholder={t('ordersPage.searchCustomer')}
                    required
                  />

                  {/* Customer search dropdown */}
                  {showCustomerDropdown && customerResults.length > 0 && (
                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                      {customerResults.map((customer) => (
                        <div
                          key={customer.id}
                          className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                          onClick={() => selectCustomer(customer)}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <User className="h-5 w-5 text-gray-400" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {customer.first_name} {customer.last_name}
                              </p>
                              <p className="text-sm text-gray-500 truncate">
                                {customer.email}
                              </p>
                              {customer.phone && (
                                <p className="text-xs text-gray-400">
                                  {customer.phone}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <div>
                  <Label htmlFor="phone">Telefono</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
              </div>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">Nome *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Cognome *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="company">Azienda</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                />
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle>Indirizzo di Spedizione</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="street">Via *</Label>
                  <Input
                    id="street"
                    value={formData.street}
                    onChange={(e) => setFormData(prev => ({ ...prev, street: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="city">Città *</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="postalCode">CAP *</Label>
                  <Input
                    id="postalCode"
                    value={formData.postalCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="country">Paese</Label>
                <Select value={formData.country} onValueChange={(value) => setFormData(prev => ({ ...prev, country: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CH">Svizzera</SelectItem>
                    <SelectItem value="IT">Italia</SelectItem>
                    <SelectItem value="DE">Germania</SelectItem>
                    <SelectItem value="FR">Francia</SelectItem>
                    <SelectItem value="AT">Austria</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Products */}
          <Card>
            <CardHeader>
              <CardTitle>Prodotti</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Aggiungi Prodotto</Label>
                <Select onValueChange={addProduct}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona un prodotto..." />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map(product => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.title} - {formatCurrency(product.discount_price || product.price)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {formData.items.length > 0 && (
                <div className="space-y-3">
                  {formData.items.map(item => (
                    <div key={item.product_id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{item.product.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {formatCurrency(item.unit_price)} per unità
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.product_id, item.quantity - 1)}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-12 text-center">{item.quantity}</span>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.product_id, item.quantity + 1)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="font-medium min-w-[80px] text-right">
                        {formatCurrency(item.total_price)}
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeItem(item.product_id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Summary */}
          {formData.items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Riepilogo Ordine</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotale:</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Spedizione:</span>
                  <span>{formatCurrency(shippingCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span>IVA (8.1%):</span>
                  <span>{formatCurrency(taxAmount)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Totale:</span>
                  <span>{formatCurrency(totalAmount)}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Note</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Note aggiuntive per l'ordine..."
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Submit */}
          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/${locale}/admin/orders`)}
            >
              Annulla
            </Button>
            <Button
              type="submit"
              disabled={creating || formData.items.length === 0}
              className="bg-primary hover:bg-primary/90"
            >
              {creating ? 'Creazione...' : 'Crea Ordine'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
