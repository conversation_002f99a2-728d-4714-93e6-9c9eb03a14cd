import Stripe from 'stripe';

// Create a function to initialize <PERSON><PERSON> to handle build-time environment variable issues
function createStripeInstance() {
  const secretKey = process.env.STRIPE_SECRET_KEY;

  if (!secretKey) {
    // During build time, environment variables might not be available
    // Return a mock object that will throw runtime errors if used
    return new Proxy({} as <PERSON><PERSON>, {
      get() {
        throw new Error('STRIPE_SECRET_KEY is not set. Please check your environment variables.');
      }
    });
  }

  return new Stripe(secretKey, {
    typescript: true,
  });
}

export const stripe = createStripeInstance();

export const getStripePublishableKey = () => {
  if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
    throw new Error('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not set');
  }
  return process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
};
