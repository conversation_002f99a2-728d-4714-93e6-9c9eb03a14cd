import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const data = await request.json()
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 403 })
    }

    const { data: level, error } = await supabase
      .from('user_levels')
      .update({
        level: data.level,
        name: data.name,
        minimum_points: data.minimum_points,
        discount_percentage: data.discount_percentage,
        points_multiplier: data.points_multiplier,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating level:', error)
      return NextResponse.json({ error: 'Fehler beim Aktualisieren des Levels' }, { status: 500 })
    }

    return NextResponse.json({ success: true, level })
  } catch (error) {
    console.error('Error in level update:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 403 })
    }

    const { error } = await supabase
      .from('user_levels')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting level:', error)
      return NextResponse.json({ error: 'Fehler beim Löschen des Levels' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in level delete:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}

