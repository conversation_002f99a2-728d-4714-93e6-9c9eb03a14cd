import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { selectGiftProduct } from '@/lib/gamification'

// POST - Select a product for a pending gift claim
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { claimId, productId } = await request.json()

    if (!claimId || !productId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const success = await selectGiftProduct(user.id, claimId, productId)

    if (!success) {
      return NextResponse.json({ error: 'Failed to select gift product' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in gift selection:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
