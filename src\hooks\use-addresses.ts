'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import type { Database } from '@/lib/supabase/database.types';
import type { User } from '@supabase/supabase-js';

type UserAddress = Database['public']['Tables']['user_addresses']['Row'];
type AddressInsert = Database['public']['Tables']['user_addresses']['Insert'];
type AddressUpdate = Database['public']['Tables']['user_addresses']['Update'];

interface UseAddressesReturn {
  addresses: UserAddress[];
  loading: boolean;
  error: string | null;
  loadAddresses: () => Promise<void>;
  createAddress: (data: Omit<AddressInsert, 'user_id'>) => Promise<void>;
  updateAddress: (id: string, data: Partial<AddressUpdate>) => Promise<void>;
  deleteAddress: (id: string) => Promise<void>;
  setDefaultAddress: (id: string, type: 'billing' | 'shipping') => Promise<void>;
}

export function useAddresses(user: User | null): UseAddressesReturn {
  const [addresses, setAddresses] = useState<UserAddress[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const t = useTranslations('account.addresses.messages');
  const supabase = createClient();

  const loadAddresses = useCallback(async () => {
    if (!user) {
      setAddresses([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { data, error: fetchError } = await supabase
        .from('user_addresses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      setAddresses(data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error loading addresses:', err);
      toast.error(t('loadError'));
    } finally {
      setLoading(false);
    }
  }, [user, supabase, t]);

  const createAddress = useCallback(async (data: Omit<AddressInsert, 'user_id'>) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      // If this is being set as default, unset other defaults of the same type
      if (data.is_default) {
        await supabase
          .from('user_addresses')
          .update({ is_default: false })
          .eq('user_id', user.id)
          .eq('type', data.type);
      }

      const { error: insertError } = await supabase
        .from('user_addresses')
        .insert({
          ...data,
          user_id: user.id,
        });

      if (insertError) {
        throw insertError;
      }

      await loadAddresses();
      toast.success(t('createSuccess'));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error creating address:', err);
      toast.error(t('createError'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user, supabase, t, loadAddresses]);

  const updateAddress = useCallback(async (id: string, data: Partial<AddressUpdate>) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      // If this is being set as default, unset other defaults of the same type
      if (data.is_default && data.type) {
        await supabase
          .from('user_addresses')
          .update({ is_default: false })
          .eq('user_id', user.id)
          .eq('type', data.type)
          .neq('id', id);
      }

      const { error: updateError } = await supabase
        .from('user_addresses')
        .update(data)
        .eq('id', id)
        .eq('user_id', user.id);

      if (updateError) {
        throw updateError;
      }

      await loadAddresses();
      toast.success(t('updateSuccess'));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error updating address:', err);
      toast.error(t('updateError'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user, supabase, t, loadAddresses]);

  const deleteAddress = useCallback(async (id: string) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      const { error: deleteError } = await supabase
        .from('user_addresses')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (deleteError) {
        throw deleteError;
      }

      await loadAddresses();
      toast.success(t('deleteSuccess'));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error deleting address:', err);
      toast.error(t('deleteError'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user, supabase, t, loadAddresses]);

  const setDefaultAddress = useCallback(async (id: string, type: 'billing' | 'shipping') => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      // First, unset all defaults of this type
      await supabase
        .from('user_addresses')
        .update({ is_default: false })
        .eq('user_id', user.id)
        .eq('type', type);

      // Then set the selected address as default
      const { error: updateError } = await supabase
        .from('user_addresses')
        .update({ is_default: true })
        .eq('id', id)
        .eq('user_id', user.id);

      if (updateError) {
        throw updateError;
      }

      await loadAddresses();
      toast.success(t('setDefaultSuccess'));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error setting default address:', err);
      toast.error(t('setDefaultError'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user, supabase, t, loadAddresses]);

  // Load addresses when user changes
  useEffect(() => {
    loadAddresses();
  }, [loadAddresses]);

  return {
    addresses,
    loading,
    error,
    loadAddresses,
    createAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
  };
}
