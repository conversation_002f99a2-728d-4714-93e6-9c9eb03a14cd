'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Minus, Plus, Trash2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { useCart } from '@/lib/cart'

interface CartItemActionsProps {
  itemId: string
  currentQuantity: number
}

export function CartItemActions({ itemId, currentQuantity }: CartItemActionsProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const { updateCartItem, removeFromCart } = useCart()

  const updateQuantity = async (newQuantity: number) => {
    if (isLoading || newQuantity < 0) return

    setIsLoading(true)
    try {
      let success = false

      if (newQuantity === 0) {
        success = await removeFromCart(itemId)
        if (success) {
          toast({
            title: "Artikel entfernt",
            description: "Der Artikel wurde aus Ihrem <PERSON>nkorb entfernt.",
          })
        }
      } else {
        success = await updateCartItem(itemId, newQuantity)
      }

      if (!success) {
        throw new Error('Failed to update cart')
      }
    } catch (error) {
      console.error('Error updating cart:', error)
      toast({
        title: "Fehler",
        description: "Der Warenkorb konnte nicht aktualisiert werden.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const removeItem = () => updateQuantity(0)
  const decreaseQuantity = () => updateQuantity(currentQuantity - 1)
  const increaseQuantity = () => updateQuantity(currentQuantity + 1)

  return (
    <div className="flex items-center gap-2">
      {/* Quantity Controls */}
      <div className="flex items-center border rounded-md">
        <Button
          variant="ghost"
          size="sm"
          onClick={decreaseQuantity}
          disabled={isLoading || currentQuantity <= 1}
          className="h-8 w-8 p-0"
        >
          <Minus className="h-3 w-3" />
        </Button>
        <span className="px-3 py-1 text-sm font-medium min-w-[2rem] text-center">
          {currentQuantity}
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={increaseQuantity}
          disabled={isLoading}
          className="h-8 w-8 p-0"
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>

      {/* Remove Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={removeItem}
        disabled={isLoading}
        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
      >
        <Trash2 className="h-3 w-3" />
      </Button>
    </div>
  )
}
