'use client'

import { useEffect } from 'react'
import Script from 'next/script'

interface GoogleTagManagerProps {
  gtmId: string
}

export function GoogleTagManager({ gtmId }: GoogleTagManagerProps) {
  useEffect(() => {
    // Initialize dataLayer
    if (typeof window !== 'undefined') {
      window.dataLayer = window.dataLayer || []
      window.gtag = function gtag(...args: unknown[]) {
        window.dataLayer.push(args)
      }
      window.gtag('js', new Date())
      window.gtag('config', gtmId)
    }
  }, [gtmId])

  return (
    <>
      {/* Google Tag Manager */}
      <Script
        id="gtm-script"
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${gtmId}`}
      />
      
      {/* GTM NoScript fallback */}
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${gtmId}`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </>
  )
}

// Analytics event tracking functions
export const trackEvent = (eventName: string, parameters?: Record<string, unknown>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters)
  }
}

export const trackPurchase = (transactionId: string, value: number, currency: string = 'CHF', items: unknown[] = []) => {
  trackEvent('purchase', {
    transaction_id: transactionId,
    value: value,
    currency: currency,
    items: items
  })
}

export const trackAddToCart = (currency: string = 'CHF', value: number, items: unknown[] = []) => {
  trackEvent('add_to_cart', {
    currency: currency,
    value: value,
    items: items
  })
}

export const trackRemoveFromCart = (currency: string = 'CHF', value: number, items: unknown[] = []) => {
  trackEvent('remove_from_cart', {
    currency: currency,
    value: value,
    items: items
  })
}

export const trackViewItem = (currency: string = 'CHF', value: number, items: unknown[] = []) => {
  trackEvent('view_item', {
    currency: currency,
    value: value,
    items: items
  })
}

export const trackBeginCheckout = (currency: string = 'CHF', value: number, items: unknown[] = []) => {
  trackEvent('begin_checkout', {
    currency: currency,
    value: value,
    items: items
  })
}

// Declare global gtag function
declare global {
  interface Window {
    dataLayer: unknown[]
    gtag: (...args: unknown[]) => void
  }
}
