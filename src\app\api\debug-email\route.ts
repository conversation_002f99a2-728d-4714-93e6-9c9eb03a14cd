import { NextResponse } from 'next/server'
import { testEmailConfiguration } from '@/lib/email'

export async function GET() {
  console.log('🧪 Debug Email: Email configuration test endpoint called');

  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 Debug Email: Email debug only available in development');
      return NextResponse.json(
        { error: 'Email debug only available in development environment' },
        { status: 403 }
      );
    }

    console.log('🧪 Debug Email: Starting email configuration test...');
    const result = await testEmailConfiguration();
    
    console.log('🧪 Debug Email: Email configuration test completed successfully');
    return NextResponse.json({
      success: true,
      message: 'Email configuration test completed successfully',
      result: result.success ? {
        messageId: result.result?.messageId,
        response: result.result?.response,
        accepted: result.result?.accepted,
        rejected: result.result?.rejected
      } : {
        error: result.error
      }
    });

  } catch (error) {
    console.error('🧪 Debug Email: Email configuration test failed:', error);
    return NextResponse.json(
      { 
        error: 'Email configuration test failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  console.log('🧪 Debug Email: Manual email test endpoint called');

  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 Debug Email: Email debug only available in development');
      return NextResponse.json(
        { error: 'Email debug only available in development environment' },
        { status: 403 }
      );
    }

    // Test SMTP configuration
    console.log('🧪 Debug Email: Testing SMTP configuration...');
    
    // Check environment variables
    const requiredVars = {
      SMTP_HOST: process.env.SMTP_HOST,
      SMTP_USER: process.env.SMTP_USER,
      SMTP_PASS: process.env.SMTP_PASS,
      SMTP_FROM: process.env.SMTP_FROM,
      SMTP_FROM_NAME: process.env.SMTP_FROM_NAME
    };

    const missingVars = Object.entries(requiredVars)
      .filter(([, value]) => !value)
      .map(([key]) => key);

    if (missingVars.length > 0) {
      console.error('🧪 Debug Email: Missing environment variables:', missingVars);
      return NextResponse.json(
        { 
          error: 'Missing SMTP environment variables', 
          missingVars 
        },
        { status: 500 }
      );
    }

    console.log('🧪 Debug Email: All SMTP environment variables are set');
    console.log('🧪 Debug Email: SMTP Configuration:', {
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT || '587',
      secure: process.env.SMTP_SECURE || 'false',
      user: process.env.SMTP_USER,
      from: process.env.SMTP_FROM,
      fromName: process.env.SMTP_FROM_NAME
    });

    // Test email sending
    const result = await testEmailConfiguration();
    
    console.log('🧪 Debug Email: Email test completed successfully');
    return NextResponse.json({
      success: true,
      message: 'Email configuration is working correctly',
      smtpConfig: {
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT || '587',
        secure: process.env.SMTP_SECURE || 'false',
        user: process.env.SMTP_USER,
        from: process.env.SMTP_FROM,
        fromName: process.env.SMTP_FROM_NAME
      },
      testResult: result.success ? {
        messageId: result.result?.messageId,
        response: result.result?.response,
        accepted: result.result?.accepted,
        rejected: result.result?.rejected
      } : {
        error: result.error
      }
    });

  } catch (error) {
    console.error('🧪 Debug Email: Email test failed:', error);
    return NextResponse.json(
      { 
        error: 'Email test failed', 
        details: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
