import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { Head<PERSON> } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { CookieBanner } from "@/components/cookie-banner";
import { Toaster } from "@/components/ui/toaster";
import { GoogleTagManager } from "@/components/analytics/google-tag-manager";
import { MetaPixel } from "@/components/analytics/meta-pixel";
import { defaultLocale } from '@/lib/i18n/config';

interface RootPageLayoutProps {
  children: React.ReactNode;
}

export async function RootPageLayout({ children }: RootPageLayoutProps) {
  // Use default locale for root pages
  const messages = await getMessages({ locale: defaultLocale });

  return (
    <>
      {/* Analytics */}
      {process.env.NEXT_PUBLIC_GTM_ID && (
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID} />
      )}
      {process.env.NEXT_PUBLIC_META_PIXEL_ID && (
        <MetaPixel pixelId={process.env.NEXT_PUBLIC_META_PIXEL_ID} />
      )}

      <NextIntlClientProvider messages={messages} locale={defaultLocale}>
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
        <CookieBanner />
        <Toaster />
      </NextIntlClientProvider>
    </>
  );
}
