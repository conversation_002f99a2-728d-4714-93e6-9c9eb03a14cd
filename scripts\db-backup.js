#!/usr/bin/env node

/**
 * Database Backup Script for PrimeCaffe
 * Creates a backup of essential production data
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function backupTable(tableName) {
  try {
    console.log(`🔄 Backing up ${tableName}...`);
    
    const { data, error } = await supabase
      .from(tableName)
      .select('*');
    
    if (error) {
      console.log(`❌ Failed to backup ${tableName}: ${error.message}`);
      return null;
    }
    
    console.log(`✅ ${tableName}: ${data.length} records`);
    return data;
  } catch (err) {
    console.log(`❌ Error backing up ${tableName}: ${err.message}`);
    return null;
  }
}

async function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(process.cwd(), 'backups');
  const backupFile = path.join(backupDir, `primecaffe-backup-${timestamp}.json`);
  
  // Ensure backup directory exists
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  console.log('📦 Creating PrimeCaffe Database Backup\n');
  
  const backup = {
    timestamp: new Date().toISOString(),
    database: 'primecaffe',
    tables: {}
  };
  
  // Essential tables to backup
  const tables = [
    'user_levels',
    'shipping_rates',
    'products',
    'bundles',
    'bundle_items',
    'coupons',
    'gift_thresholds'
  ];
  
  for (const table of tables) {
    const data = await backupTable(table);
    if (data !== null) {
      backup.tables[table] = data;
    }
  }
  
  // Write backup file
  try {
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    console.log(`\n✅ Backup created: ${backupFile}`);
    
    // Show backup summary
    console.log('\n📊 Backup Summary:');
    console.log('='.repeat(40));
    Object.entries(backup.tables).forEach(([table, data]) => {
      console.log(`${table}: ${data.length} records`);
    });
    
    return backupFile;
  } catch (err) {
    console.error(`❌ Failed to write backup file: ${err.message}`);
    return null;
  }
}

async function main() {
  const backupFile = await createBackup();
  
  if (backupFile) {
    console.log('\n🎉 Database backup completed successfully!');
    console.log(`📁 Backup location: ${backupFile}`);
    console.log('\n💡 Tip: Store this backup in a secure location');
  } else {
    console.log('\n❌ Backup failed');
    process.exit(1);
  }
}

main().catch(console.error);
