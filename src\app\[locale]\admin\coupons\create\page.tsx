'use client'

import { useState } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/hooks/use-toast'
import { AdminBackButton } from '@/components/admin/admin-back-button'
import { ArrowLeft, Percent, DollarSign } from 'lucide-react'
import Link from 'next/link'

export default function CreateCouponPage() {
  const t = useTranslations('admin')
  const locale = useLocale()
  const router = useRouter()
  const { toast } = useToast()

  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    code: '',
    type: 'percentage' as 'percentage' | 'fixed_amount',
    value: 0,
    minimumOrderAmount: '',
    usageLimit: '',
    validUntil: '',
    isActive: true
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('/api/admin/coupons/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: formData.code,
          type: formData.type,
          value: formData.value,
          minimumOrderAmount: formData.minimumOrderAmount ? parseFloat(formData.minimumOrderAmount) : null,
          usageLimit: formData.usageLimit ? parseInt(formData.usageLimit) : null,
          validUntil: formData.validUntil,
          isActive: formData.isActive
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: t('couponsPage.couponCreated'),
          description: t('couponsPage.couponCreatedDesc'),
          variant: 'default'
        })
        router.push(`/${locale}/admin/coupons`)
      } else {
        toast({
          title: t('common.error'),
          description: result.error || t('couponsPage.createError'),
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error creating coupon:', error)
      toast({
        title: t('common.error'),
        description: t('couponsPage.createError'),
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/admin/coupons`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t('couponsPage.newCoupon')}</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Percent className="h-5 w-5" />
            {t('couponsPage.createCoupon')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Coupon Code */}
            <div>
              <Label htmlFor="code">{t('couponsPage.code')} *</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                placeholder="WELCOME10"
                className="mt-2"
                required
              />
            </div>

            {/* Coupon Type */}
            <div>
              <Label>{t('couponsPage.type')} *</Label>
              <Select
                value={formData.type}
                onValueChange={(value: 'percentage' | 'fixed_amount') => 
                  setFormData({ ...formData, type: value, value: 0 })
                }
              >
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">
                    <div className="flex items-center gap-2">
                      <Percent className="h-4 w-4" />
                      {t('couponsPage.percentage')}
                    </div>
                  </SelectItem>
                  <SelectItem value="fixed_amount">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      {t('couponsPage.fixedAmount')}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Value */}
            <div>
              <Label htmlFor="value">
                {formData.type === 'percentage' 
                  ? `${t('couponsPage.percentage')} (%)` 
                  : `${t('couponsPage.amount')} (CHF)`
                } *
              </Label>
              <Input
                id="value"
                type="number"
                step={formData.type === 'percentage' ? '1' : '0.01'}
                min="0"
                max={formData.type === 'percentage' ? '100' : undefined}
                value={formData.value}
                onChange={(e) => setFormData({ ...formData, value: parseFloat(e.target.value) || 0 })}
                className="mt-2"
                required
              />
            </div>

            {/* Minimum Order Amount */}
            <div>
              <Label htmlFor="minimumOrderAmount">{t('couponsPage.minimumOrder')} (CHF)</Label>
              <Input
                id="minimumOrderAmount"
                type="number"
                step="0.01"
                min="0"
                value={formData.minimumOrderAmount}
                onChange={(e) => setFormData({ ...formData, minimumOrderAmount: e.target.value })}
                placeholder="0.00"
                className="mt-2"
              />
            </div>

            {/* Usage Limit */}
            <div>
              <Label htmlFor="usageLimit">{t('couponsPage.usageLimit')}</Label>
              <Input
                id="usageLimit"
                type="number"
                min="1"
                value={formData.usageLimit}
                onChange={(e) => setFormData({ ...formData, usageLimit: e.target.value })}
                placeholder={t('couponsPage.unlimited')}
                className="mt-2"
              />
            </div>

            {/* Valid Until */}
            <div>
              <Label htmlFor="validUntil">{t('couponsPage.validUntil')} *</Label>
              <Input
                id="validUntil"
                type="datetime-local"
                value={formData.validUntil}
                onChange={(e) => setFormData({ ...formData, validUntil: e.target.value })}
                className="mt-2"
                required
              />
            </div>

            {/* Is Active */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({ ...formData, isActive: !!checked })}
              />
              <Label htmlFor="isActive">{t('couponsPage.isActive')}</Label>
            </div>

            {/* Submit Button */}
            <div className="flex gap-4">
              <Button type="submit" disabled={loading}>
                {loading ? t('common.creating') : t('couponsPage.createCoupon')}
              </Button>
              <Button type="button" variant="outline" asChild>
                <Link href={`/${locale}/admin/coupons`}>
                  {t('common.cancel')}
                </Link>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
