import { calculateVATByCategory, getVATRateForCategory, calculateVATFromInclusive } from '@/lib/utils'

describe('VAT Category System', () => {
  describe('getVATRateForCategory', () => {
    it('should return general VAT rate when category VAT is disabled', () => {
      const settings = {
        use_category_vat: false,
        vat_rate: 0.077,
        vat_rate_coffee: 0.025,
        vat_rate_accessories: 0.081
      }

      expect(getVATRateForCategory('coffee', settings)).toBe(0.077)
      expect(getVATRateForCategory('accessories', settings)).toBe(0.077)
    })

    it('should return category-specific VAT rates when enabled', () => {
      const settings = {
        use_category_vat: true,
        vat_rate: 0.077,
        vat_rate_coffee: 0.025,
        vat_rate_accessories: 0.081
      }

      expect(getVATRateForCategory('coffee', settings)).toBe(0.025)
      expect(getVATRateForCategory('accessories', settings)).toBe(0.081)
    })

    it('should use default rates when category rates are not provided', () => {
      const settings = {
        use_category_vat: true,
        vat_rate: 0.077
      }

      expect(getVATRateForCategory('coffee', settings)).toBe(0.077)
      expect(getVATRateForCategory('accessories', settings)).toBe(0.077)
    })
  })

  describe('calculateVATFromInclusive', () => {
    it('should calculate VAT amount from inclusive price correctly', () => {
      const inclusivePrice = 107.7 // 100 + 7.7% VAT
      const vatRate = 0.077
      const vatAmount = calculateVATFromInclusive(inclusivePrice, vatRate)
      
      expect(vatAmount).toBeCloseTo(7.7, 1) // Should be approximately 7.7
    })

    it('should handle zero price', () => {
      const vatAmount = calculateVATFromInclusive(0, 0.077)
      expect(vatAmount).toBe(0)
    })
  })

  describe('calculateVATByCategory', () => {
    it('should calculate VAT breakdown by category with general VAT rate', () => {
      const items = [
        {
          quantity: 2,
          products: {
            price: 10.77, // 10 + 7.7% VAT
            discount_price: undefined,
            category: 'coffee' as const
          }
        },
        {
          quantity: 1,
          products: {
            price: 21.54, // 20 + 7.7% VAT
            discount_price: undefined,
            category: 'accessories' as const
          }
        }
      ]

      const settings = {
        use_category_vat: false,
        vat_rate: 0.077,
        vat_rate_coffee: 0.025,
        vat_rate_accessories: 0.081
      }

      const result = calculateVATByCategory(items, settings)

      expect(result.coffeeSubtotal).toBe(21.54) // 2 * 10.77
      expect(result.accessoriesSubtotal).toBe(21.54) // 1 * 21.54
      expect(result.coffeeVAT).toBeCloseTo(1.54, 2) // VAT from coffee items
      expect(result.accessoriesVAT).toBeCloseTo(1.54, 2) // VAT from accessories items
      expect(result.totalVAT).toBeCloseTo(3.08, 2) // Total VAT
    })

    it('should calculate VAT breakdown with category-specific rates', () => {
      const items = [
        {
          quantity: 1,
          products: {
            price: 102.5, // 100 + 2.5% VAT
            discount_price: undefined,
            category: 'coffee' as const
          }
        },
        {
          quantity: 1,
          products: {
            price: 108.1, // 100 + 8.1% VAT
            discount_price: undefined,
            category: 'accessories' as const
          }
        }
      ]

      const settings = {
        use_category_vat: true,
        vat_rate: 0.077,
        vat_rate_coffee: 0.025,
        vat_rate_accessories: 0.081
      }

      const result = calculateVATByCategory(items, settings)

      expect(result.coffeeSubtotal).toBe(102.5)
      expect(result.accessoriesSubtotal).toBe(108.1)
      expect(result.coffeeVAT).toBeCloseTo(2.5, 1) // 102.5 - (102.5 / 1.025)
      expect(result.accessoriesVAT).toBeCloseTo(8.1, 1) // 108.1 - (108.1 / 1.081)
      expect(result.totalVAT).toBeCloseTo(10.6, 1) // Total VAT
    })

    it('should handle discount prices correctly', () => {
      const items = [
        {
          quantity: 1,
          products: {
            price: 10.77,
            discount_price: 8.62, // 8 + 7.7% VAT
            category: 'coffee' as const
          }
        }
      ]

      const settings = {
        use_category_vat: false,
        vat_rate: 0.077,
        vat_rate_coffee: 0.025,
        vat_rate_accessories: 0.081
      }

      const result = calculateVATByCategory(items, settings)

      expect(result.coffeeSubtotal).toBe(8.62) // Should use discount price
      expect(result.coffeeVAT).toBeCloseTo(0.62, 2) // VAT from discounted price
    })

    it('should handle multiple quantities correctly', () => {
      const items = [
        {
          quantity: 3,
          products: {
            price: 10.77,
            discount_price: undefined,
            category: 'coffee' as const
          }
        }
      ]

      const settings = {
        use_category_vat: false,
        vat_rate: 0.077,
        vat_rate_coffee: 0.025,
        vat_rate_accessories: 0.081
      }

      const result = calculateVATByCategory(items, settings)

      expect(result.coffeeSubtotal).toBe(32.31) // 3 * 10.77
      expect(result.coffeeVAT).toBeCloseTo(2.31, 2) // VAT from total
    })

    it('should handle empty items array', () => {
      const items: Array<{
        quantity: number;
        products: {
          price: number;
          discount_price?: number;
          category: 'coffee' | 'accessories';
        };
      }> = []
      const settings = {
        use_category_vat: false,
        vat_rate: 0.077,
        vat_rate_coffee: 0.025,
        vat_rate_accessories: 0.081
      }

      const result = calculateVATByCategory(items, settings)

      expect(result.coffeeSubtotal).toBe(0)
      expect(result.accessoriesSubtotal).toBe(0)
      expect(result.coffeeVAT).toBe(0)
      expect(result.accessoriesVAT).toBe(0)
      expect(result.totalVAT).toBe(0)
    })
  })

  describe('Swiss VAT Rates Integration', () => {
    it('should work with realistic Swiss VAT rates', () => {
      // Swiss VAT rates: 2.5% for food (coffee), 7.7% for other goods
      const items = [
        {
          quantity: 2,
          products: {
            price: 20.50, // Coffee with 2.5% VAT
            discount_price: undefined,
            category: 'coffee' as const
          }
        },
        {
          quantity: 1,
          products: {
            price: 32.31, // Accessories with 7.7% VAT
            discount_price: undefined,
            category: 'accessories' as const
          }
        }
      ]

      const settings = {
        use_category_vat: true,
        vat_rate: 0.077,
        vat_rate_coffee: 0.025, // 2.5% for food products
        vat_rate_accessories: 0.077 // 7.7% for other goods
      }

      const result = calculateVATByCategory(items, settings)

      expect(result.coffeeSubtotal).toBe(41.0) // 2 * 20.50
      expect(result.accessoriesSubtotal).toBe(32.31)
      
      // Coffee VAT: 41.0 - (41.0 / 1.025) = 1.0
      expect(result.coffeeVAT).toBeCloseTo(1.0, 1)
      
      // Accessories VAT: 32.31 - (32.31 / 1.077) = 2.31
      expect(result.accessoriesVAT).toBeCloseTo(2.31, 2)
      
      expect(result.totalVAT).toBeCloseTo(3.31, 2)
    })
  })
})
