import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { sendOrderConfirmationEmail, sendAdminOrderNotificationEmail } from '@/lib/email'

export async function POST() {
  console.log('🧪 Complete Flow Test: Starting complete email flow test...');

  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 Complete Flow Test: Only available in development');
      return NextResponse.json(
        { error: 'Complete email flow test only available in development environment' },
        { status: 403 }
      );
    }

    // Get the most recent confirmed order from the database
    console.log('🧪 Complete Flow Test: Fetching recent confirmed order...');
    const { data: recentOrder, error: orderError } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          products (
            title
          )
        )
      `)
      .eq('status', 'confirmed')
      .eq('payment_status', 'paid')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (orderError || !recentOrder) {
      console.log('🧪 Complete Flow Test: No recent confirmed orders found, creating mock order...');
      
      // Create a comprehensive mock order for testing
      const mockOrder = {
        id: `TEST-COMPLETE-${Date.now()}`,
        email: '<EMAIL>', // Use the email from the logs
        subtotal: 7000, // 70.00 CHF
        shipping_cost: 1000, // 10.00 CHF
        tax_amount: 500, // 5.00 CHF
        discount_amount: 0,
        total_amount: 8500, // 85.00 CHF
        currency: 'CHF',
        status: 'confirmed',
        payment_status: 'paid',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: null, // Guest order
        shipping_address: {
          first_name: 'Test',
          last_name: 'Customer',
          street_address: 'Via Test 123',
          city: 'Lugano',
          postal_code: '6900',
          country: 'CH'
        },
        billing_address: {
          first_name: 'Test',
          last_name: 'Customer',
          street_address: 'Via Test 123',
          city: 'Lugano',
          postal_code: '6900',
          country: 'CH'
        },
        customer_info: {
          firstName: 'Test',
          lastName: 'Customer',
          email: '<EMAIL>',
          phone: '+41 79 123 45 67'
        },
        order_items: [
          {
            id: 'test-item-1',
            order_id: `TEST-COMPLETE-${Date.now()}`,
            product_id: 'test-product-1',
            quantity: 2,
            unit_price: 3500, // 35.00 CHF each
            total_price: 7000, // 70.00 CHF total
            product: {
              title: 'Lavazza Qualità Oro - Espresso Beans 1kg'
            },
            products: {
              title: 'Lavazza Qualità Oro - Espresso Beans 1kg'
            }
          },
          {
            id: 'test-item-2',
            order_id: `TEST-COMPLETE-${Date.now()}`,
            product_id: 'test-product-2',
            quantity: 1,
            unit_price: 1500, // 15.00 CHF
            total_price: 1500, // 15.00 CHF total
            product: {
              title: 'Illy Classico Medium Roast - Ground Coffee 250g'
            },
            products: {
              title: 'Illy Classico Medium Roast - Ground Coffee 250g'
            }
          }
        ]
      };

      console.log('🧪 Complete Flow Test: Using mock order for testing:', {
        orderId: mockOrder.id,
        email: mockOrder.email,
        totalAmount: mockOrder.total_amount,
        itemsCount: mockOrder.order_items.length
      });

      // Test both email types with mock order
      const results = {
        customerEmail: null as Record<string, unknown> | null,
        adminEmail: null as Record<string, unknown> | null,
        errors: [] as string[]
      };

      // Test customer confirmation email
      try {
        console.log('🧪 Complete Flow Test: Testing customer confirmation email...');
        const customerResult = await sendOrderConfirmationEmail(mockOrder as Parameters<typeof sendOrderConfirmationEmail>[0]);
        results.customerEmail = {
          success: true,
          messageId: customerResult.messageId,
          response: customerResult.response,
          accepted: customerResult.accepted,
          rejected: customerResult.rejected
        };
        console.log('🧪 Complete Flow Test: Customer confirmation email sent successfully');
      } catch (error) {
        console.error('🧪 Complete Flow Test: Customer confirmation email failed:', error);
        results.customerEmail = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
        results.errors.push('Customer email failed: ' + (error instanceof Error ? error.message : String(error)));
      }

      // Test admin notification email
      try {
        console.log('🧪 Complete Flow Test: Testing admin notification email...');
        const adminResult = await sendAdminOrderNotificationEmail(mockOrder as Parameters<typeof sendAdminOrderNotificationEmail>[0]);
        results.adminEmail = {
          success: true,
          messageId: adminResult.messageId,
          response: adminResult.response,
          accepted: adminResult.accepted,
          rejected: adminResult.rejected
        };
        console.log('🧪 Complete Flow Test: Admin notification email sent successfully');
      } catch (error) {
        console.error('🧪 Complete Flow Test: Admin notification email failed:', error);
        results.adminEmail = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
        results.errors.push('Admin email failed: ' + (error instanceof Error ? error.message : String(error)));
      }

      const success = results.customerEmail?.success && results.adminEmail?.success;
      console.log('🧪 Complete Flow Test: Complete email flow test finished:', {
        success,
        customerEmailSent: results.customerEmail?.success,
        adminEmailSent: results.adminEmail?.success,
        errorCount: results.errors.length
      });

      return NextResponse.json({
        success,
        message: success ? 'Complete email flow test passed successfully' : 'Complete email flow test had errors',
        results,
        mockOrder: {
          id: mockOrder.id,
          email: mockOrder.email,
          totalAmount: mockOrder.total_amount
        }
      });

    } else {
      console.log('🧪 Complete Flow Test: Using real order for testing:', {
        orderId: recentOrder.id,
        email: recentOrder.email,
        totalAmount: recentOrder.total_amount,
        itemsCount: recentOrder.order_items?.length || 0
      });

      // Test with real order
      const results = {
        customerEmail: null as Record<string, unknown> | null,
        adminEmail: null as Record<string, unknown> | null,
        errors: [] as string[]
      };

      // Test customer confirmation email
      try {
        console.log('🧪 Complete Flow Test: Testing customer confirmation email with real order...');
        const customerResult = await sendOrderConfirmationEmail(recentOrder);
        results.customerEmail = {
          success: true,
          messageId: customerResult.messageId,
          response: customerResult.response
        };
        console.log('🧪 Complete Flow Test: Customer confirmation email sent successfully');
      } catch (error) {
        console.error('🧪 Complete Flow Test: Customer confirmation email failed:', error);
        results.customerEmail = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
        results.errors.push('Customer email failed: ' + (error instanceof Error ? error.message : String(error)));
      }

      // Test admin notification email
      try {
        console.log('🧪 Complete Flow Test: Testing admin notification email with real order...');
        const adminResult = await sendAdminOrderNotificationEmail(recentOrder);
        results.adminEmail = {
          success: true,
          messageId: adminResult.messageId,
          response: adminResult.response
        };
        console.log('🧪 Complete Flow Test: Admin notification email sent successfully');
      } catch (error) {
        console.error('🧪 Complete Flow Test: Admin notification email failed:', error);
        results.adminEmail = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
        results.errors.push('Admin email failed: ' + (error instanceof Error ? error.message : String(error)));
      }

      const success = results.customerEmail?.success && results.adminEmail?.success;
      console.log('🧪 Complete Flow Test: Complete email flow test finished:', {
        success,
        customerEmailSent: results.customerEmail?.success,
        adminEmailSent: results.adminEmail?.success,
        errorCount: results.errors.length
      });

      return NextResponse.json({
        success,
        message: success ? 'Complete email flow test passed successfully' : 'Complete email flow test had errors',
        results,
        realOrder: {
          id: recentOrder.id,
          email: recentOrder.email,
          totalAmount: recentOrder.total_amount
        }
      });
    }

  } catch (error) {
    console.error('🧪 Complete Flow Test: Complete email flow test failed:', error);
    return NextResponse.json(
      { 
        error: 'Complete email flow test failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}
