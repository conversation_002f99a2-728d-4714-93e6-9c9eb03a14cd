'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { formatCurrency } from '@/lib/utils'
import { Search, Plus, Truck, MapPin, Clock, Package } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { AdminBackButton } from '@/components/admin/admin-back-button'
import Link from 'next/link'

export default function AdminShippingPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [shippingRates, setShippingRates] = useState<Array<{
    id: string;
    country: string;
    cost: number;
    free_shipping_threshold: number;
    estimated_days: string;
    is_active: boolean;
  }>>([])

  const loadShippingData = useCallback(async () => {
    try {
      console.log('Loading shipping data...')

      // Get all shipping rates
      const { data: shippingRatesData, error } = await supabase
        .from('shipping_rates')
        .select('*')
        .order('country', { ascending: true })

      if (error) {
        console.error('Error fetching shipping rates:', error)
        setLoading(false)
        return
      }

      setShippingRates(shippingRatesData || [])
      setLoading(false)
      console.log('Shipping data loaded successfully')
    } catch (error) {
      console.error('Error loading shipping data:', error)
      setLoading(false)
    }
  }, [supabase])

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        console.log('Checking authentication...')
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading shipping data')
        setAuthChecked(true)
        await loadShippingData()
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, loadShippingData])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Caricamento spedizioni...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('shippingPage.title')}</h1>
          <p className="text-muted-foreground">
            {t('shippingPage.subtitle')}
          </p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/admin/shipping/create`}>
            <Plus className="mr-2 h-4 w-4" />
            {t('shippingPage.newShippingZone')}
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('shippingPage.searchPlaceholder')}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shipping Statistics */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('shippingPage.shippingZones')}</p>
                <p className="text-2xl font-bold">{shippingRates?.length || 0}</p>
              </div>
              <MapPin className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('shippingPage.activeZones')}</p>
                <p className="text-2xl font-bold">
                  {shippingRates?.filter(rate => rate.is_active).length || 0}
                </p>
              </div>
              <Truck className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('shippingPage.avgShippingCost')}</p>
                <p className="text-2xl font-bold">
                  {shippingRates && shippingRates.length > 0
                    ? formatCurrency(shippingRates.reduce((sum, rate) => sum + rate.cost, 0) / shippingRates.length)
                    : formatCurrency(0)
                  }
                </p>
              </div>
              <Package className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('shippingPage.avgDeliveryTime')}</p>
                <p className="text-2xl font-bold">
                  {shippingRates && shippingRates.length > 0
                    ? `${Math.round(shippingRates.reduce((sum, rate) => {
                        // Extract first number from estimated_days string
                        const days = parseInt(rate.estimated_days.match(/\d+/)?.[0] || '0');
                        return sum + days;
                      }, 0) / shippingRates.length)} ${t('shippingPage.days')}`
                    : `0 ${t('shippingPage.days')}`
                  }
                </p>
              </div>
              <Clock className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Shipping Rates Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('shippingPage.allShippingZones')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">{t('shippingPage.country')}</th>
                  <th className="text-left py-3 px-4">{t('shippingPage.shippingCost')}</th>
                  <th className="text-left py-3 px-4">{t('shippingPage.freeShippingFrom')}</th>
                  <th className="text-left py-3 px-4">{t('shippingPage.deliveryTime')}</th>
                  <th className="text-left py-3 px-4">{t('shippingPage.status')}</th>
                  <th className="text-left py-3 px-4">{t('shippingPage.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {shippingRates?.map((rate) => {
                  const countryFlags: Record<string, string> = {
                    'CH': '🇨🇭',
                    'DE': '🇩🇪',
                    'AT': '🇦🇹',
                    'FR': '🇫🇷',
                    'IT': '🇮🇹'
                  };

                  const getCountryName = (countryCode: string) => {
                    const countryKey = `shippingPage.countries.${countryCode}` as const;
                    return t(countryKey) !== countryKey ? t(countryKey) : countryCode;
                  };
                  
                  return (
                    <tr key={rate.id} className="border-b hover:bg-muted/50">
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <span className="text-2xl">{countryFlags[rate.country] || '🌍'}</span>
                          <div>
                            <div className="font-medium">
                              {getCountryName(rate.country)}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {rate.country}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-semibold">
                          {formatCurrency(rate.cost)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {formatCurrency(rate.free_shipping_threshold)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {rate.estimated_days}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge variant={rate.is_active ? "default" : "secondary"}>
                          {rate.is_active ? t('shippingPage.active') : t('shippingPage.inactive')}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/${locale}/admin/shipping/${rate.id}`}>
                              {t('shippingPage.edit')}
                            </Link>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
            {(!shippingRates || shippingRates.length === 0) && (
              <div className="text-center py-8 text-muted-foreground">
                {t('shippingPage.noShippingZones')}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Setup for Common Countries */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>{t('shippingPage.quickSetup')}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            {t('shippingPage.quickSetupDescription')}
          </p>
          <div className="grid md:grid-cols-3 gap-4">
            <Button variant="outline" className="justify-start">
              {t('shippingPage.setupSwitzerland')}
            </Button>
            <Button variant="outline" className="justify-start">
              {t('shippingPage.setupGermany')}
            </Button>
            <Button variant="outline" className="justify-start">
              {t('shippingPage.setupAustria')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
