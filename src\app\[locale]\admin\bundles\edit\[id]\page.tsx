import { createClient } from '@/lib/supabase/server'
import { redirect, notFound } from 'next/navigation'
import EditBundleContent from '@/components/admin/edit-bundle-content'

interface EditBundlePageProps {
  params: Promise<{ locale: string; id: string }>;
}

export default async function EditBundlePage({ params }: EditBundlePageProps) {
  const { locale, id } = await params;
  const supabase = await createClient()

  // Check if user is authenticated and is admin
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect(`/${locale}/login`)
  }

  // Check admin status
  const { data: profile } = await supabase
    .from('users')
    .select('is_admin')
    .eq('id', user.id)
    .single()

  if (!profile?.is_admin) {
    redirect(`/${locale}`)
  }

  // Get bundle data
  const { data: bundle, error } = await supabase
    .from('bundles')
    .select(`
      *,
      bundle_items (
        quantity,
        products (
          id,
          title,
          price,
          discount_price,
          images
        )
      )
    `)
    .eq('id', id)
    .single()

  if (error || !bundle) {
    notFound()
  }

  return <EditBundleContent locale={locale} bundle={bundle} />
}
