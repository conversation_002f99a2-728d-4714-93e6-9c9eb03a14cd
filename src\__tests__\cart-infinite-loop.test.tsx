import { renderHook, waitFor } from '@testing-library/react'
import { useCart } from '@/lib/cart'

// Mock Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    auth: {
      getUser: jest.fn().mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('getUser timeout')), 100) // Keep short for tests
        )
      )
    },
    from: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          is: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  single: jest.fn().mockResolvedValue({ data: null, error: { code: 'PGRST116' } })
                })
              })
            })
          })
        })
      })
    })
  })
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('useCart - Infinite Loop Prevention', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue('test-session-id')
  })

  it('should not enter infinite loop when auth times out', async () => {
    const { result } = renderHook(() => useCart())

    // Initially loading should be true
    expect(result.current.loading).toBe(true)

    // Wait for the hook to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    }, { timeout: 5000 })

    // Cart should be empty but not loading
    expect(result.current.cart.items).toEqual([])
    expect(result.current.loading).toBe(false)
  })

  it('should handle multiple simultaneous refreshCart calls', async () => {
    const { result } = renderHook(() => useCart())

    // Wait for initial load to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    }, { timeout: 5000 })

    // Call refreshCart multiple times simultaneously
    const promises = [
      result.current.refreshCart(),
      result.current.refreshCart(),
      result.current.refreshCart()
    ]

    await Promise.all(promises)

    // Should complete without hanging
    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    }, { timeout: 5000 })
  })

  it('should complete within reasonable time even with auth timeout', async () => {
    const startTime = Date.now()
    
    const { result } = renderHook(() => useCart())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    }, { timeout: 10000 })

    const endTime = Date.now()
    const duration = endTime - startTime

    // Should complete within 10 seconds even with timeouts
    expect(duration).toBeLessThan(10000)
    expect(result.current.cart.items).toEqual([])
  })
})
