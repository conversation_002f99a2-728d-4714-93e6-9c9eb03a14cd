import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Get all bundles with their items
    const { data: bundles, error } = await supabase
      .from('bundles')
      .select(`
        *,
        bundle_items (
          quantity,
          products (
            id,
            title,
            price,
            discount_price
          )
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching bundles:', error)
      return NextResponse.json(
        { error: '<PERSON><PERSON> beim <PERSON>' },
        { status: 500 }
      )
    }

    return NextResponse.json(bundles)
  } catch (error) {
    console.error('Error in bundles get:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const bundleData = await request.json()
    
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Validate required fields
    if (!bundleData.title || !bundleData.total_price || !bundleData.items || bundleData.items.length === 0) {
      return NextResponse.json(
        { error: 'Titel, Gesamtpreis und mindestens ein Produkt sind erforderlich' },
        { status: 400 }
      )
    }

    // Generate slug
    const slug = bundleData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')

    // Create bundle
    const { data: bundle, error: bundleError } = await supabase
      .from('bundles')
      .insert({
        title: bundleData.title,
        description: bundleData.description || '',
        slug,
        image: bundleData.image || '',
        total_price: bundleData.total_price,
        discount_price: bundleData.discount_price || null,
        is_available: bundleData.is_available ?? true,
      })
      .select()
      .single()

    if (bundleError) {
      console.error('Error creating bundle:', bundleError)
      return NextResponse.json(
        { error: 'Fehler beim Erstellen des Bundles' },
        { status: 500 }
      )
    }

    // Create bundle items
    const bundleItems = bundleData.items.map((item: { product_id: string; quantity: number }) => ({
      bundle_id: bundle.id,
      product_id: item.product_id,
      quantity: item.quantity
    }))

    const { error: itemsError } = await supabase
      .from('bundle_items')
      .insert(bundleItems)

    if (itemsError) {
      console.error('Error creating bundle items:', itemsError)
      // Rollback bundle creation
      await supabase.from('bundles').delete().eq('id', bundle.id)
      return NextResponse.json(
        { error: 'Fehler beim Erstellen der Bundle-Artikel' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true, bundle })
  } catch (error) {
    console.error('Error in bundle create:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}
