import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// GET - Get public gamification configuration (no auth required)
export async function GET() {
  try {
    const supabase = await createClient()

    // Get league configuration
    const { data: leagueConfig, error: leagueError } = await supabase
      .from('league_config')
      .select('*')
      .eq('is_active', true)
      .single()

    if (leagueError) {
      console.error('Error fetching league config:', leagueError)
    }

    // Get points per CHF from site settings
    const { data: settings, error: settingsError } = await supabase
      .from('site_settings')
      .select('points_per_chf')
      .eq('store_name', 'PrimeCaffe')
      .single()

    if (settingsError) {
      console.error('Error fetching site settings:', settingsError)
    }

    const pointsPerChf = settings?.points_per_chf || 1

    return NextResponse.json({
      leagueConfig: leagueConfig || null,
      pointsPerChf: pointsPerChf
    })
  } catch (error) {
    console.error('Error in gamification config GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
