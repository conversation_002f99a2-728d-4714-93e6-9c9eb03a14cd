import { CartManager } from '@/lib/cart'

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn()
}

jest.mock('@/lib/supabase/client', () => ({
  createClient: () => mockSupabaseClient
}))

// Mock cart session
jest.mock('@/lib/cart-session', () => ({
  getClientCartSessionId: () => 'test-session-id',
  clearClientCartSession: jest.fn()
}))

// Mock safeGetUser
jest.mock('@/lib/supabase/helpers', () => ({
  safeGetUser: jest.fn()
}))

describe('Cart System Improvements', () => {
  let cartManager: CartManager
  
  beforeEach(() => {
    jest.clearAllMocks()
    cartManager = new CartManager()
    
    // Setup default mocks
    mockSupabaseClient.auth.getUser.mockResolvedValue({ 
      data: { user: null }, 
      error: null 
    })
    
    const createMockQueryChain = (result = { data: null, error: { code: 'PGRST116' } }) => ({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          is: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  maybeSingle: jest.fn().mockResolvedValue(result)
                })
              })
            })
          })
        })
      })
    })

    mockSupabaseClient.from.mockReturnValue(createMockQueryChain())
  })

  describe('Database Query Optimization', () => {
    it('should use single query with joins instead of separate queries', async () => {
      const mockCartData = {
        id: 'cart-123',
        user_id: 'user-123',
        status: 'active',
        total_amount: 50.00,
        cart_items: [
          {
            id: 'item-1',
            product_id: 'product-1',
            quantity: 2,
            products: {
              id: 'product-1',
              title: 'Test Coffee',
              price: 25.00,
              category: 'coffee'
            }
          }
        ]
      }

      const createMockQueryChain = (result = { data: mockCartData, error: null }) => ({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  maybeSingle: jest.fn().mockResolvedValue(result)
                })
              })
            })
          })
        })
      })

      mockSupabaseClient.from.mockReturnValue(createMockQueryChain())

      const result = await cartManager.getCart('user-123')
      
      expect(result).toBeTruthy()
      expect(result?.items).toHaveLength(1)
      expect(result?.items[0].product?.title).toBe('Test Coffee')
      
      // Verify single query was made with proper joins
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('carts')
      const selectCall = mockSupabaseClient.from().select
      expect(selectCall).toHaveBeenCalledWith(expect.stringContaining('cart_items'))
      expect(selectCall).toHaveBeenCalledWith(expect.stringContaining('products'))
    })

    it('should handle increased timeout gracefully', async () => {
      const startTime = Date.now()
      
      // Mock a slow query that takes 10 seconds (within new 15s timeout)
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  maybeSingle: jest.fn().mockImplementation(() => 
                    new Promise(resolve => 
                      setTimeout(() => resolve({ data: null, error: { code: 'PGRST116' } }), 10000)
                    )
                  )
                })
              })
            })
          })
        })
      })

      const result = await cartManager.getCart('user-123')
      const duration = Date.now() - startTime
      
      expect(result).toBeNull()
      expect(duration).toBeGreaterThan(9000) // Should wait for the full query
      expect(duration).toBeLessThan(16000) // But not exceed the 15s timeout
    }, 20000)
  })

  describe('Circuit Breaker Improvements', () => {
    it('should track consecutive timeouts separately', async () => {
      // Simulate timeout errors
      const timeoutError = new Error('Database query timeout')

      const createMockQueryChain = () => ({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            is: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                order: jest.fn().mockReturnValue({
                  limit: jest.fn().mockReturnValue({
                    maybeSingle: jest.fn().mockRejectedValue(timeoutError)
                  })
                })
              })
            })
          })
        })
      })

      mockSupabaseClient.from.mockReturnValue(createMockQueryChain())

      // Make 3 consecutive calls that timeout
      await cartManager.getCart('user-123')
      await cartManager.getCart('user-123')
      await cartManager.getCart('user-123')

      const metrics = cartManager.getPerformanceMetrics()
      expect(metrics.circuitBreakerStatus).toBe('OPEN')
      expect(metrics.consecutiveTimeouts).toBe(3)
    })

    it('should reset circuit breaker after successful operation', async () => {
      // First, trigger circuit breaker
      const timeoutError = new Error('Database query timeout')
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  maybeSingle: jest.fn().mockRejectedValue(timeoutError)
                })
              })
            })
          })
        })
      })

      // Trigger failures
      await cartManager.getCart('user-123')
      await cartManager.getCart('user-123')
      await cartManager.getCart('user-123')

      let metrics = cartManager.getPerformanceMetrics()
      expect(metrics.circuitBreakerStatus).toBe('OPEN')

      // Now mock successful response
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  maybeSingle: jest.fn().mockResolvedValue({ 
                    data: null, 
                    error: { code: 'PGRST116' } 
                  })
                })
              })
            })
          })
        })
      })

      // Wait for circuit breaker reset time (simulate)
      await new Promise(resolve => setTimeout(resolve, 100))
      
      await cartManager.getCart('user-123')
      
      metrics = cartManager.getPerformanceMetrics()
      expect(metrics.failureCount).toBe(0)
      expect(metrics.consecutiveTimeouts).toBe(0)
    })
  })

  describe('Performance Monitoring', () => {
    it('should track performance metrics correctly', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  maybeSingle: jest.fn().mockResolvedValue({ 
                    data: null, 
                    error: { code: 'PGRST116' } 
                  })
                })
              })
            })
          })
        })
      })

      await cartManager.getCart('user-123')
      await cartManager.getCart('user-456')

      const metrics = cartManager.getPerformanceMetrics()
      
      expect(metrics.totalRequests).toBe(2)
      expect(metrics.successfulRequests).toBe(2)
      expect(metrics.failedRequests).toBe(0)
      expect(metrics.successRate).toBe(100)
      expect(metrics.averageResponseTime).toBeGreaterThan(0)
    })

    it('should track cache hit rate', async () => {
      const mockCartData = {
        id: 'cart-123',
        user_id: 'user-123',
        status: 'active',
        total_amount: 50.00,
        cart_items: []
      }

      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  maybeSingle: jest.fn().mockResolvedValue({ 
                    data: mockCartData, 
                    error: null 
                  })
                })
              })
            })
          })
        })
      })

      // First call - cache miss
      await cartManager.getCart('user-123')
      
      // Second call - cache hit
      await cartManager.getCart('user-123')

      const metrics = cartManager.getPerformanceMetrics()
      
      expect(metrics.cacheHits).toBe(1)
      expect(metrics.cacheMisses).toBe(1)
      expect(metrics.cacheHitRate).toBe(50)
    })
  })

  describe('Retry Logic', () => {
    it('should retry failed requests with exponential backoff', async () => {
      let callCount = 0
      const mockError = new Error('Network error')

      const createMockQueryChain = () => ({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            is: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                order: jest.fn().mockReturnValue({
                  limit: jest.fn().mockReturnValue({
                    maybeSingle: jest.fn().mockImplementation(() => {
                      callCount++
                      if (callCount < 4) { // Changed to 4 to account for initial + 3 retries
                        return Promise.reject(mockError)
                      }
                      return Promise.resolve({ data: null, error: { code: 'PGRST116' } })
                    })
                  })
                })
              })
            })
          })
        })
      })

      mockSupabaseClient.from.mockReturnValue(createMockQueryChain())

      const startTime = Date.now()
      const result = await cartManager.getCart('user-123')
      const duration = Date.now() - startTime

      expect(result).toBeNull()
      expect(callCount).toBe(4) // Initial call + 3 retries
      expect(duration).toBeGreaterThan(1000) // Should have delays between retries
    }, 10000)
  })
})
