import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { generateLevelsFromConfig } from '@/lib/gamification'

// POST - Regenerate levels from current configuration
export async function POST() {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Regenerate levels
    await generateLevelsFromConfig()

    return NextResponse.json({ 
      success: true, 
      message: 'Levels regenerated successfully based on current league configuration' 
    })
  } catch (error) {
    console.error('Error regenerating levels:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
