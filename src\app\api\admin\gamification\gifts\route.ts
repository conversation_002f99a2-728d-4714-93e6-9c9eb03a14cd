import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// GET - Fetch all gift rewards
export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { data: rewards, error } = await supabase
      .from('gift_rewards')
      .select('*')
      .order('type', { ascending: true })
      .order('trigger_value', { ascending: true })

    if (error) {
      console.error('Error fetching gift rewards:', error)
      return NextResponse.json({ error: '<PERSON><PERSON> beim <PERSON>' }, { status: 500 })
    }

    return NextResponse.json({ rewards })
  } catch (error) {
    console.error('Error in gift rewards fetch:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}

// POST - Update gift reward
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const data = await request.json()

    const { data: reward, error } = await supabase
      .from('gift_rewards')
      .update({
        option_1_product_id: data.option_1_product_id || null,
        option_2_product_id: data.option_2_product_id || null,
        option_3_product_id: data.option_3_product_id || null,
        is_active: data.is_active ?? true,
      })
      .eq('id', data.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating gift reward:', error)
      return NextResponse.json({ error: 'Fehler beim Aktualisieren' }, { status: 500 })
    }

    return NextResponse.json({ success: true, reward })
  } catch (error) {
    console.error('Error in gift reward update:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}

