"use client";

import { useTranslations } from 'next-intl';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Target } from 'lucide-react';

export default function CookiesPage() {
  const t = useTranslations('cookiePolicy');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-6">
            <Cookie className="h-8 w-8 text-yellow-600" />
          </div>
          <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-muted-foreground">
            {t('description')}
          </p>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>{t('whatAreCookies')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('whatAreCookiesDescription')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                {t('necessary.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p className="mb-4">
                <span className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                  {t('alwaysActive')}
                </span>
              </p>
              <p>
                {t('necessary.description')}
              </p>
              <ul>
                <li>{t('sessionCookies')}</li>
                <li>{t('cartCookies')}</li>
                <li>{t('loginCookies')}</li>
                <li>{t('securityCookies')}</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="h-5 w-5" />
                {t('analytics.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('analytics.description')}
              </p>
              <ul>
                <li>{t('googleAnalytics')}</li>
                <li>{t('heatmaps')}</li>
                <li>{t('performanceMonitoring')}</li>
              </ul>
              <p>
                {t('analyticsImprovement')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                {t('marketing.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('marketing.description')}
              </p>
              <ul>
                <li>{t('metaPixel')}</li>
                <li>{t('googleAds')}</li>
                <li>{t('retargeting')}</li>
              </ul>
              <p>
                {t('marketingConsent')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('manageCookieSettings')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('manageCookieDescription')}
              </p>
              <ul>
                <li>{t('cookieBanner')}</li>
                <li>{t('browserSettings')}</li>
                <li>{t('optOutLinks')}</li>
              </ul>

              <h4>{t('browserInstructions')}</h4>
              <ul>
                <li>{t('chrome')}</li>
                <li>{t('firefox')}</li>
                <li>{t('safari')}</li>
                <li>{t('edge')}</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('cookieDeactivationEffects')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('deactivationDescription')}
              </p>
              <ul>
                <li>{t('cartMayNotWork')}</li>
                <li>{t('loginRequired')}</li>
                <li>{t('noPersonalization')}</li>
                <li>{t('featuresUnavailable')}</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('contact')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                {t('contactDescription')}
              </p>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('email')}</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
                <div>
                  <p className="font-medium">{t('phone')}</p>
                  <p className="text-sm text-muted-foreground">+41 79 342 65 74</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
