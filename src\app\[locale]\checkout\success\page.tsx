'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Package, Mail, ArrowRight } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface Order {
  id: string
  order_number?: string
  total_amount: number
  status: string
  payment_status: string
  created_at: string
  order_items: Array<{
    quantity: number
    unit_price: number
    total_price: number
    products: {
      title: string
    }
  }>
}

function CheckoutSuccessContent() {
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const searchParams = useSearchParams()
  const router = useRouter()
  const t = useTranslations('checkout.success')
  const locale = useLocale()

  const paymentIntentId = searchParams.get('payment_intent')

  useEffect(() => {
    const confirmPayment = async () => {
      if (!paymentIntentId) {
        setError('Payment information missing')
        setLoading(false)
        return
      }

      try {
        const response = await fetch('/api/checkout/confirm-payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ paymentIntentId }),
        })

        const data = await response.json()

        if (data.success) {
          // Fetch order details
          console.log(`🛒 Success: Fetching order details for order ID: ${data.orderId}`)
          const orderResponse = await fetch(`/api/orders/${data.orderId}`)
          console.log(`🛒 Success: Order response status: ${orderResponse.status}`)

          if (orderResponse.ok) {
            const orderData = await orderResponse.json()
            console.log(`🛒 Success: Order data received:`, orderData)
            setOrder(orderData)
          } else {
            const errorData = await orderResponse.json()
            console.error(`🛒 Success: Failed to fetch order:`, errorData)
            setError(`Failed to load order details: ${errorData.error || 'Unknown error'}`)
          }
        } else if (data.requiresAction) {
          // Redirect back to checkout for additional authentication
          router.push(`/${locale}/checkout?payment_intent=${paymentIntentId}`)
          return
        } else {
          setError(data.error || 'Payment failed')
        }
      } catch (err) {
        console.error('Error confirming payment:', err)
        setError('Failed to confirm payment')
      } finally {
        setLoading(false)
      }
    }

    confirmPayment()
  }, [paymentIntentId, router, locale])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
              <p>{t('confirmingPayment') || 'Confirming your payment...'}</p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-red-500 mb-4">
                <Package className="h-12 w-12 mx-auto" />
              </div>
              <h1 className="text-2xl font-bold mb-4">{t('paymentFailed') || 'Payment Failed'}</h1>
              <p className="text-gray-600 mb-6">{error}</p>
              <Button onClick={() => router.push(`/${locale}/cart`)}>
                {t('backToCart') || 'Back to Cart'}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <p>{t('orderNotFound') || 'Order not found'}</p>
              <Button onClick={() => router.push(`/${locale}`)} className="mt-4">
                {t('goHome') || 'Go Home'}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="mb-6">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-4">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-4">
            {t('title')}
          </h1>
          <p className="text-muted-foreground max-w-md mx-auto">
            {t('description')}
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t('orderDetails')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-muted p-4 rounded-lg">
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>{t('orderNumber')}:</span>
                  <span className="font-mono">#{order.order_number || order.id.slice(-8)}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('date')}:</span>
                  <span>{new Date(order.created_at).toLocaleDateString(locale === 'it' ? 'it-CH' : locale === 'fr' ? 'fr-CH' : 'de-CH')}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>{t('status')}:</span>
                  <Badge className="bg-green-100 text-green-800 capitalize">
                    {order.status}
                  </Badge>
                </div>
                <div className="flex justify-between font-semibold text-base pt-2 border-t">
                  <span>{t('total')}:</span>
                  <span>{formatCurrency(order.total_amount)}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-3">{t('orderedItems')}</h3>
              <div className="space-y-2">
                {order.order_items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b">
                    <div>
                      <p className="font-medium">{item.products.title}</p>
                      <p className="text-sm text-muted-foreground">{t('quantity')}: {item.quantity}</p>
                    </div>
                    <p className="font-medium">{formatCurrency(item.total_price)}</p>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-start space-x-3">
                <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">{t('emailConfirmation')}</h4>
                  <p className="text-sm text-blue-700">
                    {t('emailConfirmationDesc')}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-amber-50 p-4 rounded-lg">
              <div className="flex items-start space-x-3">
                <Package className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-amber-900">{t('shipping')}</h4>
                  <p className="text-sm text-amber-700">
                    {t('shippingDesc')}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={() => router.push(`/${locale}`)}
                variant="outline"
                className="flex-1"
              >
                {t('backToShop')}
              </Button>
              <Button
                onClick={() => router.push(`/${locale}/coffee-box-builder`)}
                className="flex-1"
              >
                Coffee Box Builder
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default function CheckoutSuccessPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p>Loading...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    }>
      <CheckoutSuccessContent />
    </Suspense>
  )
}
