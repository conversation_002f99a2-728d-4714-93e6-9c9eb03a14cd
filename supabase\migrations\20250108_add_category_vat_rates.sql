-- Add category-specific VAT rates to site_settings
-- Migration: 20250108_add_category_vat_rates

-- Add category-specific VAT rate columns to site_settings table
ALTER TABLE site_settings 
ADD COLUMN IF NOT EXISTS vat_rate_coffee DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS vat_rate_accessories DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS use_category_vat BOOLEAN DEFAULT FALSE;

-- Update existing settings to use category-specific VAT rates
-- Set default values based on current vat_rate if they exist
UPDATE site_settings 
SET 
    vat_rate_coffee = COALESCE(vat_rate, 0.077),
    vat_rate_accessories = COALESCE(vat_rate, 0.077),
    use_category_vat = FALSE
WHERE vat_rate_coffee IS NULL OR vat_rate_accessories IS NULL;

-- Insert default settings if no settings exist
INSERT INTO site_settings (
    store_name,
    default_currency,
    timezone,
    vat_rate,
    vat_rate_coffee,
    vat_rate_accessories,
    use_category_vat,
    vat_included_in_prices,
    vat_number
) 
SELECT 
    'PrimeCaffe',
    'CHF',
    'Europe/Zurich',
    0.077,
    0.077,
    0.077,
    false,
    true,
    null
WHERE NOT EXISTS (SELECT 1 FROM site_settings);

-- Add comments to document the new columns
COMMENT ON COLUMN site_settings.vat_rate_coffee IS 'VAT rate for coffee products (decimal, e.g., 0.077 for 7.7%)';
COMMENT ON COLUMN site_settings.vat_rate_accessories IS 'VAT rate for accessories products (decimal, e.g., 0.077 for 7.7%)';
COMMENT ON COLUMN site_settings.use_category_vat IS 'Whether to use category-specific VAT rates instead of general vat_rate';
