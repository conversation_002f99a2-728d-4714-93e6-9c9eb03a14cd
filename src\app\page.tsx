import { redirect } from 'next/navigation';
import { headers } from 'next/headers';
import { locales, defaultLocale } from '@/lib/i18n/config';

export const dynamic = 'force-dynamic';

export default async function RootPage() {
  // Get the Accept-Language header to detect browser language
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';

  // Parse the Accept-Language header to find the best matching locale
  const browserLocales = acceptLanguage
    .split(',')
    .map(lang => lang.split(';')[0].trim().toLowerCase())
    .map(lang => lang.split('-')[0]); // Get just the language part (e.g., 'de' from 'de-CH')

  // Find the first browser language that matches our supported locales
  const detectedLocale = browserLocales.find(lang =>
    locales.includes(lang as 'de' | 'fr' | 'it')
  ) || defaultLocale;

  redirect(`/${detectedLocale}`);
}
