'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { formatCurrency } from '@/lib/utils'
import { Search, Plus, Percent, Calendar, Users, TrendingUp } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { AdminBackButton } from '@/components/admin/admin-back-button'

export default function AdminCouponsPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [coupons, setCoupons] = useState<Array<{
    id: string;
    code: string;
    type: string;
    value: number;
    minimum_order_amount?: number;
    usage_limit?: number;
    used_count?: number;
    valid_until: string;
    is_active: boolean;
    created_at: string;
  }>>([])

  const loadCouponsData = useCallback(async () => {
    try {
      console.log('Loading coupons data...')

      // Get all coupons
      const { data: couponsData, error } = await supabase
        .from('coupons')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching coupons:', error)
        setLoading(false)
        return
      }

      setCoupons(couponsData || [])
      setLoading(false)
      console.log('Coupons data loaded successfully')
    } catch (error) {
      console.error('Error loading coupons data:', error)
      setLoading(false)
    }
  }, [supabase])

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        console.log('Checking authentication...')
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading coupons data')
        setAuthChecked(true)
        await loadCouponsData()
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, loadCouponsData])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Caricamento coupon...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('couponsPage.title')}</h1>
          <p className="text-muted-foreground">
            {t('couponsPage.subtitle')}
          </p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/admin/coupons/create`}>
            <Plus className="mr-2 h-4 w-4" />
            {t('couponsPage.newCoupon')}
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('couponsPage.searchPlaceholder')}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Coupon Statistics */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('couponsPage.totalCoupons')}</p>
                <p className="text-2xl font-bold">{coupons?.length || 0}</p>
              </div>
              <Percent className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('couponsPage.activeCoupons')}</p>
                <p className="text-2xl font-bold">
                  {coupons?.filter(coupon => coupon.is_active).length || 0}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('couponsPage.expired')}</p>
                <p className="text-2xl font-bold">
                  {coupons?.filter(coupon => new Date(coupon.valid_until) < new Date()).length || 0}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('couponsPage.used')}</p>
                <p className="text-2xl font-bold">
                  {coupons?.reduce((sum, coupon) => sum + (coupon.used_count || 0), 0) || 0}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Coupons Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('couponsPage.allCoupons')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">{t('couponsPage.code')}</th>
                  <th className="text-left py-3 px-4">{t('couponsPage.type')}</th>
                  <th className="text-left py-3 px-4">{t('couponsPage.value')}</th>
                  <th className="text-left py-3 px-4">{t('couponsPage.minimumOrder')}</th>
                  <th className="text-left py-3 px-4">{t('couponsPage.usage')}</th>
                  <th className="text-left py-3 px-4">{t('couponsPage.validUntil')}</th>
                  <th className="text-left py-3 px-4">{t('couponsPage.status')}</th>
                  <th className="text-left py-3 px-4">{t('couponsPage.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {coupons?.map((coupon) => {
                  const isExpired = new Date(coupon.valid_until) < new Date();
                  const usageText = coupon.usage_limit 
                    ? `${coupon.used_count || 0}/${coupon.usage_limit}`
                    : `${coupon.used_count || 0}/∞`;
                  
                  return (
                    <tr key={coupon.id} className="border-b hover:bg-muted/50">
                      <td className="py-3 px-4">
                        <div className="font-mono font-semibold">
                          {coupon.code}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge variant="outline">
                          {coupon.type === 'percentage' ? t('couponsPage.percentage') : t('couponsPage.fixedAmount')}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-semibold">
                          {coupon.type === 'percentage' 
                            ? `${coupon.value}%`
                            : formatCurrency(coupon.value)
                          }
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {coupon.minimum_order_amount
                            ? formatCurrency(coupon.minimum_order_amount)
                            : t('couponsPage.noMinimum')
                          }
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {usageText}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {new Date(coupon.valid_until).toLocaleDateString(locale === 'it' ? 'it-IT' : locale === 'fr' ? 'fr-FR' : 'de-DE')}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-1">
                          {isExpired ? (
                            <Badge variant="destructive">{t('couponsPage.expired')}</Badge>
                          ) : coupon.is_active ? (
                            <Badge variant="default">{t('couponsPage.active')}</Badge>
                          ) : (
                            <Badge variant="secondary">{t('couponsPage.inactive')}</Badge>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/${locale}/admin/coupons/edit/${coupon.id}`}>
                              {t('couponsPage.edit')}
                            </Link>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
            {(!coupons || coupons.length === 0) && (
              <div className="text-center py-8 text-muted-foreground">
                {t('couponsPage.noCoupons')}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
