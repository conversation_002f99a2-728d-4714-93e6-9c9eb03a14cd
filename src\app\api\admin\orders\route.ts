import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    console.log('🔐 Admin orders API called')
    
    // Check if user is authenticated and is admin
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      console.log('❌ No authenticated user')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (profileError || !profile?.is_admin) {
      console.log('❌ User is not admin')
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    console.log('✅ User is admin, fetching orders...')

    // Get all orders with customer information using admin client
    const { data: ordersData, error } = await supabaseAdmin
      .from('orders')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Error fetching orders:', error)
      return NextResponse.json({ 
        error: 'Failed to fetch orders',
        details: error.message 
      }, { status: 500 })
    }

    console.log('✅ Orders fetched:', ordersData?.length)

    // Get user data for each order that has a user_id
    const ordersWithUsers = await Promise.all(
      (ordersData || []).map(async (order) => {
        if (order.user_id) {
          const { data: userData, error: userError } = await supabaseAdmin
            .from('users')
            .select('first_name, last_name, email')
            .eq('id', order.user_id)
            .single()
          
          if (userError) {
            console.error('❌ Error fetching user for order:', order.id, userError)
          }
          
          return {
            ...order,
            users: userData
          }
        }
        return {
          ...order,
          users: null
        }
      })
    )

    console.log('✅ Orders with users prepared:', ordersWithUsers.length)

    return NextResponse.json({ 
      success: true, 
      orders: ordersWithUsers 
    })

  } catch (error) {
    console.error('❌ Unexpected error in admin orders API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
