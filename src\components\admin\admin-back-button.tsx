'use client'

import { Button } from '@/components/ui/button'
import { ArrowLeft, LayoutDashboard } from 'lucide-react'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'

interface AdminBackButtonProps {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  className?: string
  showIcon?: boolean
  showText?: boolean
}

export function AdminBackButton({ 
  variant = 'outline',
  size = 'default',
  className = '',
  showIcon = true,
  showText = true
}: AdminBackButtonProps) {
  const t = useTranslations('admin')
  const locale = useLocale()

  return (
    <Button 
      variant={variant} 
      size={size} 
      className={`${className}`}
      asChild
    >
      <Link href={`/${locale}/admin`}>
        {showIcon && <ArrowLeft className="mr-2 h-4 w-4" />}
        {showText && t('backToDashboard')}
      </Link>
    </Button>
  )
}

// Alternative version with dashboard icon
export function AdminDashboardButton({ 
  variant = 'outline',
  size = 'default',
  className = '',
  showIcon = true,
  showText = true
}: AdminBackButtonProps) {
  const t = useTranslations('admin')
  const locale = useLocale()

  return (
    <Button 
      variant={variant} 
      size={size} 
      className={`${className}`}
      asChild
    >
      <Link href={`/${locale}/admin`}>
        {showIcon && <LayoutDashboard className="mr-2 h-4 w-4" />}
        {showText && t('backToDashboard')}
      </Link>
    </Button>
  )
}
