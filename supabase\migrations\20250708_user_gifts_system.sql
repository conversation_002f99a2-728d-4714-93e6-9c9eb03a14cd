-- Migration to create user gifts system
-- This migration creates the user_gifts table and fixes gift_thresholds schema

-- First, fix the gift_thresholds table to use points instead of amount
-- Check if the column exists and rename it
DO $$
BEGIN
    -- Check if threshold_amount exists and threshold_points doesn't
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gift_thresholds' 
        AND column_name = 'threshold_amount'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'gift_thresholds' 
        AND column_name = 'threshold_points'
    ) THEN
        -- Rename threshold_amount to threshold_points and change type
        ALTER TABLE gift_thresholds 
        RENAME COLUMN threshold_amount TO threshold_points;
        
        -- Change the data type from DECIMAL to INTEGER
        ALTER TABLE gift_thresholds 
        ALTER COLUMN threshold_points TYPE INTEGER USING threshold_points::INTEGER;
    END IF;
END $$;

-- Create user_gifts table to track gifts received by users
CREATE TABLE IF NOT EXISTS user_gifts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    gift_threshold_id UUID NOT NULL REFERENCES gift_thresholds(id) ON DELETE RESTRICT,
    products_received UUID[] NOT NULL DEFAULT '{}', -- Array of product IDs received
    points_used INTEGER NOT NULL DEFAULT 0, -- Points threshold reached to get this gift
    received_at TIMESTAMPTZ DEFAULT NOW(),
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL, -- Optional: link to order that triggered the gift
    notes TEXT, -- Optional notes about the gift
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_gifts_user_id ON user_gifts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_gifts_received_at ON user_gifts(received_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_gifts_gift_threshold_id ON user_gifts(gift_threshold_id);

-- Add updated_at trigger for user_gifts
CREATE TRIGGER update_user_gifts_updated_at 
    BEFORE UPDATE ON user_gifts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on user_gifts table
ALTER TABLE user_gifts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_gifts
-- Users can view their own gifts
CREATE POLICY "Users can view own gifts" ON user_gifts
    FOR SELECT USING (auth.uid() = user_id);

-- Admins can view all gifts
CREATE POLICY "Admins can view all gifts" ON user_gifts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );

-- Only the system (service role) can insert gifts
-- This will be done through the gamification system
CREATE POLICY "Service role can insert gifts" ON user_gifts
    FOR INSERT WITH CHECK (true);

-- Admins can update gifts (for corrections)
CREATE POLICY "Admins can update gifts" ON user_gifts
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );

-- Admins can delete gifts (for corrections)
CREATE POLICY "Admins can delete gifts" ON user_gifts
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );
