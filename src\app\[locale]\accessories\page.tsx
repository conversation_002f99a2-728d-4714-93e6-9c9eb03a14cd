"use client";

import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Coffee, ShoppingCart } from 'lucide-react';

export default function AccessoriesPage() {
  const t = useTranslations('accessories');
  const tCommon = useTranslations('common');

  // Mock accessories data
  const accessories = [
    {
      id: 1,
      name: "Espresso Tassen Set",
      description: "Elegantes 2er Set Espresso-Tassen aus hochwertigem Porzellan",
      price: 29.90,
      image: "/images/accessories/espresso-cups.jpg"
    },
    {
      id: 2,
      name: "Milchaufschäumer",
      description: "Elektrischer Milchaufschäumer für perfekten Milchschaum",
      price: 49.90,
      image: "/images/accessories/milk-frother.jpg"
    },
    {
      id: 3,
      name: "Ka<PERSON>l-Aufbewahrung",
      description: "Stilvolle Aufbewahrungsbox für bis zu 60 Kapseln",
      price: 19.90,
      image: "/images/accessories/capsule-storage.jpg"
    },
    {
      id: 4,
      name: "Kaffee-Thermometer",
      description: "Digitales Thermometer für die perfekte Brühtemperatur",
      price: 24.90,
      image: "/images/accessories/thermometer.jpg"
    },
    {
      id: 5,
      name: "Reinigungstabletten",
      description: "Spezielle Reinigungstabletten für Kaffeemaschinen (10 Stück)",
      price: 12.90,
      image: "/images/accessories/cleaning-tablets.jpg"
    },
    {
      id: 6,
      name: "Kaffee-Waage",
      description: "Präzise Digitalwaage für die perfekte Kaffeemenge",
      price: 39.90,
      image: "/images/accessories/coffee-scale.jpg"
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-100 rounded-full mb-6">
            <Coffee className="h-8 w-8 text-amber-600" />
          </div>
          <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {accessories.map((accessory) => (
            <Card key={accessory.id} className="overflow-hidden">
              <div className="aspect-square bg-muted flex items-center justify-center">
                <Coffee className="h-16 w-16 text-muted-foreground" />
              </div>
              <CardHeader>
                <CardTitle className="text-lg">{accessory.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  {accessory.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">
                    CHF {accessory.price.toFixed(2)}
                  </span>
                  <Button size="sm" className="flex items-center gap-2">
                    <ShoppingCart className="h-4 w-4" />
                    {tCommon('addToCart')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="mt-12">
          <CardHeader>
            <CardTitle>Warum PrimeCaffe Zubehör?</CardTitle>
          </CardHeader>
          <CardContent className="prose prose-sm max-w-none">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Premium Qualität</h4>
                <p className="text-muted-foreground">
                  Alle unsere Zubehörprodukte werden sorgfältig ausgewählt und entsprechen höchsten Qualitätsstandards.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Perfekte Ergänzung</h4>
                <p className="text-muted-foreground">
                  Unser Zubehör ist speziell auf unsere Kaffeekapseln und -bohnen abgestimmt für das beste Ergebnis.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Einfache Pflege</h4>
                <p className="text-muted-foreground">
                  Alle Produkte sind einfach zu reinigen und zu pflegen, damit Sie lange Freude daran haben.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Schweizer Service</h4>
                <p className="text-muted-foreground">
                  Bei Fragen oder Problemen steht Ihnen unser Schweizer Kundenservice gerne zur Verfügung.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
