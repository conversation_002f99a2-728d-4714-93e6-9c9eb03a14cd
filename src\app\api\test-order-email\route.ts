import { NextResponse } from 'next/server'
import { sendOrderConfirmationEmail, sendAdminOrderNotificationEmail } from '@/lib/email'

export async function POST() {
  console.log('🧪 API: Test order email endpoint called');

  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 API: Test order email only available in development');
      return NextResponse.json(
        { error: 'Test order email only available in development environment' },
        { status: 403 }
      );
    }

    // Create a mock order for testing
    const mockOrder = {
      id: 'TEST-ORDER-' + Date.now(),
      order_number: 'PC250999',
      email: process.env.NEXT_PUBLIC_COMPANY_EMAIL || '<EMAIL>', // Use a valid email for testing
      status: 'confirmed',
      subtotal: 4500, // 45.00 CHF
      shipping_cost: 500, // 5.00 CHF
      discount_amount: 0,
      total_amount: 5000, // 50.00 CHF
      created_at: new Date().toISOString(),
      shipping_address: {
        first_name: 'Test',
        last_name: 'Customer',
        street_address: 'Test Street 123',
        postal_code: '8001',
        city: 'Zurich',
        country: 'Switzerland'
      },
      billing_address: {
        first_name: 'Test',
        last_name: 'Customer',
        street_address: 'Test Street 123',
        postal_code: '8001',
        city: 'Zurich',
        country: 'Switzerland'
      },
      items: [
        {
          quantity: 2,
          unit_price: 1500, // 15.00 CHF
          total_price: 3000, // 30.00 CHF
          product: {
            title: 'Test Coffee Blend'
          }
        },
        {
          quantity: 1,
          unit_price: 1500, // 15.00 CHF
          total_price: 1500, // 15.00 CHF
          product: {
            title: 'Premium Espresso'
          }
        }
      ]
    };

    console.log('🧪 API: Sending test order emails with mock data:', {
      orderId: mockOrder.id,
      email: mockOrder.email,
      totalAmount: mockOrder.total_amount,
      itemsCount: mockOrder.items.length
    });

    const results = {
      customerEmail: null as { success: boolean; messageId?: string; response?: string; error?: string } | null,
      adminEmail: null as { success: boolean; messageId?: string; response?: string; error?: string } | null,
      errors: [] as string[]
    };

    // Test customer confirmation email
    try {
      console.log('🧪 API: Testing customer confirmation email...');
      const customerResult = await sendOrderConfirmationEmail(mockOrder);
      results.customerEmail = {
        success: true,
        messageId: customerResult.messageId,
        response: customerResult.response
      };
      console.log('🧪 API: Customer confirmation email test successful');
    } catch (error) {
      console.error('🧪 API: Customer confirmation email test failed:', error);
      results.customerEmail = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
      results.errors.push('Customer email failed: ' + (error instanceof Error ? error.message : String(error)));
    }

    // Test admin notification email
    try {
      console.log('🧪 API: Testing admin notification email...');
      const adminResult = await sendAdminOrderNotificationEmail(mockOrder);
      results.adminEmail = {
        success: true,
        messageId: adminResult.messageId,
        response: adminResult.response
      };
      console.log('🧪 API: Admin notification email test successful');
    } catch (error) {
      console.error('🧪 API: Admin notification email test failed:', error);
      results.adminEmail = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
      results.errors.push('Admin email failed: ' + (error instanceof Error ? error.message : String(error)));
    }

    const overallSuccess = results.customerEmail?.success && results.adminEmail?.success;

    console.log('🧪 API: Test order email completed:', {
      success: overallSuccess,
      customerEmailSuccess: results.customerEmail?.success,
      adminEmailSuccess: results.adminEmail?.success,
      errorCount: results.errors.length
    });

    return NextResponse.json({
      success: overallSuccess,
      message: overallSuccess 
        ? 'All test order emails sent successfully' 
        : 'Some test order emails failed',
      results,
      mockOrder: {
        id: mockOrder.id,
        email: mockOrder.email,
        totalAmount: mockOrder.total_amount
      }
    }, { status: overallSuccess ? 200 : 500 });

  } catch (error) {
    console.error('🧪 API: Exception in test-order-email endpoint:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Test order email endpoint. Use POST to run test.',
    available: process.env.NODE_ENV === 'development'
  });
}
