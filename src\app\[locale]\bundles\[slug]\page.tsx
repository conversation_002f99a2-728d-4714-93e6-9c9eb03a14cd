import { createClient } from '@/lib/supabase/server'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import { formatCurrency } from '@/lib/utils'
import { getTranslations } from 'next-intl/server'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AddToCartButton } from '@/components/cart/add-to-cart-button'
import { ArrowLeft, Package, ShoppingCart, Gift } from 'lucide-react'
import Link from 'next/link'
import { defaultLocale } from '@/lib/i18n/config'

export const dynamic = 'force-dynamic'

interface BundleDetailPageProps {
  params: Promise<{ locale: string; slug: string }>
}

export default async function BundleDetailPage({ params }: BundleDetailPageProps) {
  const { locale, slug } = await params
  const supabase = await createClient()

  // Get bundle data by slug
  const { data: bundle, error } = await supabase
    .from('bundles')
    .select(`
      *,
      bundle_items (
        quantity,
        products (
          id,
          title,
          description,
          price,
          discount_price,
          images,
          brand,
          category,
          coffee_type
        )
      )
    `)
    .eq('slug', slug)
    .eq('is_available', true)
    .single()

  if (error || !bundle) {
    notFound()
  }

  const t = await getTranslations({ locale, namespace: 'bundles' })
  const tNav = await getTranslations({ locale, namespace: 'navigation' })
  const tAdmin = await getTranslations({ locale, namespace: 'admin' })

  const hasDiscount = bundle.discount_price && bundle.discount_price < bundle.total_price
  const displayPrice = hasDiscount ? bundle.discount_price : bundle.total_price
  const savings = hasDiscount ? bundle.total_price - bundle.discount_price : 0
  const savingsPercentage = hasDiscount ? Math.round((savings / bundle.total_price) * 100) : 0

  // Generate localized paths with proper locale handling
  const getLocalizedPath = (path: string) => {
    return locale === defaultLocale ? path : `/${locale}${path}`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="ghost" asChild>
            <Link href={getLocalizedPath('/bundles')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {tNav('bundles')}
            </Link>
          </Button>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 items-start">
          {/* Bundle Image */}
          <div className="w-full aspect-square relative rounded-lg overflow-hidden">
            {bundle.image ? (
              <Image 
                src={bundle.image} 
                alt={bundle.title} 
                fill 
                className="object-cover" 
              />
            ) : (
              <div className="w-full h-full bg-gradient-card flex items-center justify-center">
                <Package className="h-20 w-20 text-muted-foreground" />
              </div>
            )}
            {hasDiscount && (
              <Badge className="absolute top-4 right-4 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-medium">
                -{savingsPercentage}%
              </Badge>
            )}
          </div>

          {/* Bundle Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-4">{bundle.title}</h1>
              <p className="text-muted-foreground whitespace-pre-line">
                {bundle.description}
              </p>
            </div>

            {/* Pricing */}
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <span className="text-3xl font-bold">
                  {formatCurrency(displayPrice)}
                </span>
                {hasDiscount && (
                  <span className="text-xl text-muted-foreground line-through">
                    {formatCurrency(bundle.total_price)}
                  </span>
                )}
              </div>
              {hasDiscount && (
                <div className="flex items-center gap-2">
                  <Badge variant="destructive" className="text-white">
                    {t('savings')} {formatCurrency(savings)}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    (-{savingsPercentage}%)
                  </span>
                </div>
              )}
            </div>

            {/* Add to Cart */}
            <div className="flex gap-3">
              <AddToCartButton
                productId={bundle.id}
                className="flex-1"
                isBundle={true}
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                {t('addToCart')}
              </AddToCartButton>
            </div>
          </div>
        </div>

        {/* Included Products */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">{t('includedProducts')}</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {bundle.bundle_items?.map((item: {
              quantity: number;
              products: {
                id: string;
                title: string;
                description: string;
                price: number;
                discount_price?: number;
                images: string[];
                brand?: string;
                category: string;
                coffee_type?: string;
              };
            }, index: number) => {
              const product = item.products
              const productHasDiscount = product.discount_price && product.discount_price < product.price
              const productDisplayPrice = productHasDiscount ? (product.discount_price || product.price) : product.price
              
              return (
                <Card key={index} className="overflow-hidden">
                  <div className="aspect-square relative bg-muted">
                    {product.images?.[0] ? (
                      <Image
                        src={product.images[0]}
                        alt={product.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <Package className="h-12 w-12 text-muted-foreground" />
                      </div>
                    )}
                    <div className="absolute top-2 right-2">
                      <Badge variant="secondary">
                        {item.quantity}x
                      </Badge>
                    </div>
                  </div>
                  
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg line-clamp-2">
                      {product.title}
                    </CardTitle>
                    {product.brand && (
                      <p className="text-sm text-muted-foreground">
                        {product.brand}
                      </p>
                    )}
                  </CardHeader>
                  
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">
                          {formatCurrency(productDisplayPrice)}
                        </span>
                        {productHasDiscount && (
                          <span className="text-sm text-muted-foreground line-through">
                            {formatCurrency(product.price)}
                          </span>
                        )}
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {tAdmin('bundleManagement.perPiece')}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <div className="bg-gradient-card rounded-lg p-8">
            <Gift className="h-12 w-12 text-primary mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              {t('callToAction.title')}
            </h3>
            <p className="text-muted-foreground mb-6">
              {t('callToAction.description')}
            </p>
            <Button asChild>
              <Link href={getLocalizedPath('/coffee-box-builder')}>
                Coffee Box Builder
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
