// Test script for the simplified league system
// Run with: node test-league-system.js

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://kbpbjzdlravmjntzvntv.supabase.co'
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseKey) {
  console.error('SUPABASE_SERVICE_ROLE_KEY environment variable is required')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testLeagueSystem() {
  console.log('🧪 Testing Simplified League System...\n')

  try {
    // 1. Get current league configuration
    console.log('1. Current League Configuration:')
    const { data: config, error: configError } = await supabase
      .from('league_config')
      .select('*')
      .eq('is_active', true)
      .single()

    if (configError) {
      console.error('Error fetching config:', configError)
      return
    }

    console.log(`   Levels per league: ${config.levels_per_league}`)
    console.log(`   League points: [${config.league_1_points}, ${config.league_2_points}, ${config.league_3_points}, ${config.league_4_points}, ${config.league_5_points}]`)
    console.log(`   League discounts: [${config.league_1_discount}%, ${config.league_2_discount}%, ${config.league_3_discount}%, ${config.league_4_discount}%, ${config.league_5_discount}%]`)
    console.log(`   Global multiplier: ${config.global_multiplier}x\n`)

    // 2. Test level generation
    console.log('2. Testing Level Generation:')
    const { error: genError } = await supabase.rpc('generate_levels_from_config')
    
    if (genError) {
      console.error('Error generating levels:', genError)
      return
    }
    console.log('   ✅ Levels generated successfully\n')

    // 3. Check generated levels
    console.log('3. Sample Generated Levels:')
    const { data: levels, error: levelsError } = await supabase
      .from('user_levels')
      .select('level, name, minimum_points, discount_percentage')
      .in('level', [1, 5, 10, 11, 15, 20, 21, 25, 30, 31, 35, 40, 41, 45, 50])
      .order('level')

    if (levelsError) {
      console.error('Error fetching levels:', levelsError)
      return
    }

    levels.forEach(level => {
      console.log(`   Level ${level.level.toString().padStart(2)}: ${level.name.padEnd(12)} - ${level.minimum_points.toString().padStart(5)} points - ${level.discount_percentage}% discount`)
    })

    // 4. Count total levels
    const { count, error: countError } = await supabase
      .from('user_levels')
      .select('*', { count: 'exact', head: true })

    if (countError) {
      console.error('Error counting levels:', countError)
      return
    }

    console.log(`\n4. Total Levels Generated: ${count}`)
    console.log(`   Expected: ${config.levels_per_league * 5}`)
    console.log(`   ✅ ${count === config.levels_per_league * 5 ? 'Correct!' : 'Mismatch!'}`)

    console.log('\n🎉 League system test completed successfully!')

  } catch (error) {
    console.error('Test failed:', error)
  }
}

testLeagueSystem()
