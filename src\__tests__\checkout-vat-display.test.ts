import { calculateVATByCategory } from '@/lib/utils'

describe('Checkout VAT Display', () => {
  it('should calculate correct VAT for coffee products with category-specific rates', () => {
    // Simulate Espresso Blend Bohnen (coffee) - 2 units at 26.90 CHF each
    const items = [
      {
        quantity: 2,
        products: {
          price: 26.90,
          discount_price: undefined,
          category: 'coffee' as const
        }
      }
    ]

    const vatSettings = {
      use_category_vat: true,
      vat_rate: 0.077,
      vat_rate_coffee: 0.025,
      vat_rate_accessories: 0.077
    }

    const result = calculateVATByCategory(items, vatSettings)

    // Expected calculations:
    // Subtotal: 2 * 26.90 = 53.80 CHF
    // Coffee VAT (2.5%): 53.80 - (53.80 / 1.025) = 1.31 CHF
    expect(result.coffeeSubtotal).toBe(53.80)
    expect(result.accessoriesSubtotal).toBe(0)
    expect(result.coffeeVAT).toBeCloseTo(1.31, 2)
    expect(result.accessoriesVAT).toBe(0)
    expect(result.totalVAT).toBeCloseTo(1.31, 2)
  })

  it('should calculate correct VAT for mixed cart with coffee and accessories', () => {
    const items = [
      {
        quantity: 1,
        products: {
          price: 26.90, // Coffee
          discount_price: undefined,
          category: 'coffee' as const
        }
      },
      {
        quantity: 1,
        products: {
          price: 32.31, // Accessories (30 + 7.7% VAT)
          discount_price: undefined,
          category: 'accessories' as const
        }
      }
    ]

    const vatSettings = {
      use_category_vat: true,
      vat_rate: 0.077,
      vat_rate_coffee: 0.025,
      vat_rate_accessories: 0.077
    }

    const result = calculateVATByCategory(items, vatSettings)

    // Expected calculations:
    // Coffee: 26.90 - (26.90 / 1.025) = 0.66 CHF VAT
    // Accessories: 32.31 - (32.31 / 1.077) = 2.31 CHF VAT
    // Total VAT: 0.66 + 2.31 = 2.97 CHF
    expect(result.coffeeSubtotal).toBe(26.90)
    expect(result.accessoriesSubtotal).toBe(32.31)
    expect(result.coffeeVAT).toBeCloseTo(0.66, 2)
    expect(result.accessoriesVAT).toBeCloseTo(2.31, 2)
    expect(result.totalVAT).toBeCloseTo(2.97, 2)
  })

  it('should fall back to general VAT rate when category VAT is disabled', () => {
    const items = [
      {
        quantity: 2,
        products: {
          price: 26.90,
          discount_price: undefined,
          category: 'coffee' as const
        }
      }
    ]

    const vatSettings = {
      use_category_vat: false, // Disabled
      vat_rate: 0.077,
      vat_rate_coffee: 0.025,
      vat_rate_accessories: 0.077
    }

    const result = calculateVATByCategory(items, vatSettings)

    // Should use general VAT rate (7.7%) instead of coffee rate (2.5%)
    // Expected: 53.80 - (53.80 / 1.077) = 3.85 CHF VAT
    expect(result.coffeeSubtotal).toBe(53.80)
    expect(result.coffeeVAT).toBeCloseTo(3.85, 2)
    expect(result.totalVAT).toBeCloseTo(3.85, 2)
  })
})
