const CART_SESSION_COOKIE = 'cart_session_id'
const MAX_AGE = 60 * 60 * 24 * 30 // 30 days

// Server-side functions that use next/headers - only for use in Server Components
export async function getServerCartSessionId() {
  // Dynamic import to avoid issues when used in client components
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()
  let session = cookieStore.get(CART_SESSION_COOKIE)?.value
  if (!session) {
    session = `sess_${Date.now()}_${Math.random().toString(36).slice(2,9)}`
    cookieStore.set(CART_SESSION_COOKIE, session, { path: '/', maxAge: MAX_AGE })
  }
  return session
}

export async function clearServerCartSession() {
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()
  cookieStore.set(CART_SESSION_COOKIE, '', { path: '/', maxAge: 0 })
}

export function getClientCartSessionId() {
  if (typeof document === 'undefined') return 'server-session'
  const match = document.cookie.match(new RegExp(`(?:^|; )${CART_SESSION_COOKIE}=([^;]*)`))
  if (match?.[1]) return match[1]
  const session = `sess_${Date.now()}_${Math.random().toString(36).slice(2,9)}`
  document.cookie = `${CART_SESSION_COOKIE}=${session}; path=/; max-age=${MAX_AGE}`
  return session
}

export function clearClientCartSession() {
  if (typeof document === 'undefined') return
  document.cookie = `${CART_SESSION_COOKIE}=; path=/; max-age=0`
}
