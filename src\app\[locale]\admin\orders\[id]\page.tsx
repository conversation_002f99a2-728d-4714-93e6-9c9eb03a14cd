'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useRouter, usePara<PERSON> } from 'next/navigation'
import { useLocale, useTranslations } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, Package, Truck, CheckCircle, XCircle, Clock } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { toast } from 'sonner'
import Link from 'next/link'

interface OrderItem {
  id: string
  quantity: number
  unit_price: number
  total_price: number
  products: {
    id: string
    title: string
    price: number
    discount_price?: number
  }
}

interface Order {
  id: string
  order_number?: string
  user_id?: string
  email: string
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  subtotal: number
  shipping_cost: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  currency: string
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_intent_id?: string
  shipping_address: {
    firstName: string
    lastName: string
    street: string
    city: string
    postalCode: string
    country: string
  }
  billing_address: {
    firstName: string
    lastName: string
    street: string
    city: string
    postalCode: string
    country: string
  }
  tracking_number?: string
  notes?: string
  created_at: string
  updated_at: string
  order_items: OrderItem[]
  users?: {
    first_name: string
    last_name: string
    email: string
  }
}

export default function AdminOrderDetailPage() {
  const locale = useLocale()
  const router = useRouter()
  const params = useParams()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [order, setOrder] = useState<Order | null>(null)
  const [updating, setUpdating] = useState(false)
  const [newStatus, setNewStatus] = useState<'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'>('pending')
  const [newTrackingNumber, setNewTrackingNumber] = useState<string>('')
  const [newNotes, setNewNotes] = useState<string>('')

  const orderId = params.id as string

  const loadOrderData = useCallback(async () => {
    try {
      console.log('Loading order data for ID:', orderId)

      const { data: orderData, error } = await supabase
        .from('orders')
        .select(`
          *,
          order_items (
            *,
            products (
              id,
              title,
              price,
              discount_price
            )
          ),
          users (
            first_name,
            last_name,
            email
          )
        `)
        .eq('id', orderId)
        .single()

      if (error) {
        console.error('Error fetching order:', error)
        setLoading(false)
        return
      }

      setOrder(orderData)
      setNewStatus(orderData.status)
      setNewTrackingNumber(orderData.tracking_number || '')
      setNewNotes(orderData.notes || '')
      setLoading(false)
      console.log('Order data loaded successfully')
    } catch (error) {
      console.error('Error loading order data:', error)
      setLoading(false)
    }
  }, [supabase, orderId])

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        console.log('Checking auth and loading order data...')

        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
          console.log('No user found, redirecting to login')
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.id)

        // Check if user is admin
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Error fetching profile:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading order data')
        setAuthChecked(true)
        await loadOrderData()
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, orderId, loadOrderData])

  const handleUpdateOrder = async () => {
    if (!order) return

    setUpdating(true)
    try {
      const updates: {
        status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
        tracking_number?: string
        notes?: string
      } = {}
      
      if (newStatus !== order.status) {
        updates.status = newStatus
      }
      
      if (newTrackingNumber !== (order.tracking_number || '')) {
        if (newTrackingNumber.trim()) {
          updates.tracking_number = newTrackingNumber.trim()
        } else {
          // For database update, we'll handle null separately
          updates.tracking_number = undefined
        }
      }

      if (newNotes !== (order.notes || '')) {
        updates.notes = newNotes.trim() || undefined
      }

      // Prepare database updates
      const dbUpdates: {
        status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
        tracking_number?: string | null
        notes?: string | null
      } = {}
      if (newStatus !== order.status) {
        dbUpdates.status = newStatus
      }
      if (newTrackingNumber !== (order.tracking_number || '')) {
        dbUpdates.tracking_number = newTrackingNumber.trim() || null
      }
      if (newNotes !== (order.notes || '')) {
        dbUpdates.notes = newNotes.trim() || null
      }

      if (Object.keys(dbUpdates).length === 0) {
        toast.info('Nessuna modifica da salvare')
        setUpdating(false)
        return
      }

      // Use API endpoint to update order (which handles email notifications)
      const response = await fetch(`/api/orders/${order.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dbUpdates),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Error updating order:', errorData)
        toast.error(t('orderDetail.updateError'))
        setUpdating(false)
        return
      }

      const updatedOrder = await response.json()

      // Update local state with the response from the API
      setOrder(prev => prev ? {
        ...prev,
        status: updatedOrder.status,
        tracking_number: updatedOrder.tracking_number || undefined,
        notes: updatedOrder.notes || undefined
      } : null)

      if (dbUpdates.status) {
        toast.success(t('orderDetail.statusUpdated'))
        // Show additional message if shipping notification was sent
        if (dbUpdates.status === 'shipped' && (dbUpdates.tracking_number || updatedOrder.tracking_number)) {
          toast.success(t('orderDetail.shippingNotificationSent'))
        }
      }
      if (dbUpdates.tracking_number !== undefined) {
        toast.success(t('orderDetail.trackingUpdated'))
      }
      if (dbUpdates.notes !== undefined) {
        toast.success(t('orderDetail.notesUpdated'))
      }

      setUpdating(false)
    } catch (error) {
      console.error('Error updating order:', error)
      toast.error(t('orderDetail.updateError'))
      setUpdating(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'confirmed':
        return <Package className="h-4 w-4" />
      case 'processing':
        return <Package className="h-4 w-4" />
      case 'shipped':
        return <Truck className="h-4 w-4" />
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'processing':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200'
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'refunded':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Caricamento ordine...</p>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-muted-foreground">Ordine non trovato</p>
          <Button asChild className="mt-4">
            <Link href={`/${locale}/admin/orders`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t('backToDashboard')}
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/${locale}/admin/orders`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('backToDashboard')}
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold">{t('orderDetail.title')}</h1>
          <p className="text-muted-foreground">{t('orderDetail.subtitle')}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Details */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderDetail.orderInfo')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.orderId')}
                  </Label>
                  <p className="font-mono text-sm">#{order.order_number || order.id.slice(0, 8)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.orderDate')}
                  </Label>
                  <p className="text-sm">
                    {new Date(order.created_at).toLocaleDateString(locale === 'it' ? 'it-IT' : locale === 'fr' ? 'fr-FR' : 'de-DE')}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.orderStatus')}
                  </Label>
                  <Badge className={`${getStatusColor(order.status)} flex items-center gap-1 w-fit mt-1`}>
                    {getStatusIcon(order.status)}
                    {t(`orderDetail.statuses.${order.status}`)}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.paymentStatus')}
                  </Label>
                  <Badge className={`${getPaymentStatusColor(order.payment_status)} w-fit mt-1`}>
                    {t(`orderDetail.paymentStatuses.${order.payment_status}`)}
                  </Badge>
                </div>
              </div>

              {order.tracking_number && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.trackingNumber')}
                  </Label>
                  <p className="font-mono text-sm">{order.tracking_number}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderDetail.customerInfo')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.customerName')}
                  </Label>
                  <p className="text-sm">
                    {order.users ?
                      `${order.users.first_name} ${order.users.last_name}` :
                      `${order.shipping_address.firstName} ${order.shipping_address.lastName}`
                    }
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.customerEmail')}
                  </Label>
                  <p className="text-sm">{order.email}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.shippingAddress')}
                  </Label>
                  <div className="text-sm space-y-1 mt-1">
                    <p>{order.shipping_address.firstName} {order.shipping_address.lastName}</p>
                    <p>{order.shipping_address.street}</p>
                    <p>{order.shipping_address.postalCode} {order.shipping_address.city}</p>
                    <p>{order.shipping_address.country}</p>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    {t('orderDetail.billingAddress')}
                  </Label>
                  <div className="text-sm space-y-1 mt-1">
                    <p>{order.billing_address.firstName} {order.billing_address.lastName}</p>
                    <p>{order.billing_address.street}</p>
                    <p>{order.billing_address.postalCode} {order.billing_address.city}</p>
                    <p>{order.billing_address.country}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderDetail.orderItems')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.order_items.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{item.products.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {t('orderDetail.quantity')}: {item.quantity} × {formatCurrency(item.unit_price)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{formatCurrency(item.total_price)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>


        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderDetail.orderSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('orderDetail.subtotal')}</span>
                <span>{formatCurrency(order.subtotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('orderDetail.shipping')}</span>
                <span>{formatCurrency(order.shipping_cost)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('orderDetail.tax')}</span>
                <span>{formatCurrency(order.tax_amount)}</span>
              </div>
              {order.discount_amount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>{t('orderDetail.discount')}</span>
                  <span>-{formatCurrency(order.discount_amount)}</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>{t('orderDetail.total')}</span>
                <span>{formatCurrency(order.total_amount)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Order Management */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderDetail.updateStatus')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="status">{t('orderDetail.orderStatus')}</Label>
                <Select value={newStatus} onValueChange={(value) => setNewStatus(value as 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled')}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">{t('orderDetail.statuses.pending')}</SelectItem>
                    <SelectItem value="confirmed">{t('orderDetail.statuses.confirmed')}</SelectItem>
                    <SelectItem value="processing">{t('orderDetail.statuses.processing')}</SelectItem>
                    <SelectItem value="shipped">{t('orderDetail.statuses.shipped')}</SelectItem>
                    <SelectItem value="delivered">{t('orderDetail.statuses.delivered')}</SelectItem>
                    <SelectItem value="cancelled">{t('orderDetail.statuses.cancelled')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="tracking">{t('orderDetail.trackingNumber')}</Label>
                <Input
                  id="tracking"
                  value={newTrackingNumber}
                  onChange={(e) => setNewTrackingNumber(e.target.value)}
                  placeholder="Inserisci numero tracking..."
                />
              </div>

              <div>
                <Label htmlFor="notes">{t('orderDetail.notes')}</Label>
                <textarea
                  id="notes"
                  value={newNotes}
                  onChange={(e) => setNewNotes(e.target.value)}
                  placeholder="Inserisci note aggiuntive..."
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none"
                  rows={3}
                />
              </div>

              <Button
                onClick={handleUpdateOrder}
                disabled={updating}
                className="w-full"
              >
                {updating ? t('common.loading') : t('orderDetail.saveChanges')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
