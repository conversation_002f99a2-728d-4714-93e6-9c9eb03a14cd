import { NextResponse } from 'next/server'
import { testEmailConfiguration } from '@/lib/email'

export async function POST() {
  console.log('🧪 API: Test email endpoint called');

  try {
    // Check if this is a development environment or admin request
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 API: Test email only available in development');
      return NextResponse.json(
        { error: 'Test email only available in development environment' },
        { status: 403 }
      );
    }

    console.log('🧪 API: Starting email configuration test...');
    const testResult = await testEmailConfiguration();

    if (testResult.success) {
      console.log('🧪 API: Email test completed successfully');
      return NextResponse.json({
        success: true,
        message: 'Email configuration test passed',
        result: testResult.result
      });
    } else {
      console.log('🧪 API: Email test failed');
      return NextResponse.json({
        success: false,
        message: 'Email configuration test failed',
        error: testResult.error
      }, { status: 500 });
    }

  } catch (error) {
    console.error('🧪 API: Exception in test-email endpoint:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Email test endpoint. Use POST to run test.',
    available: process.env.NODE_ENV === 'development'
  });
}
