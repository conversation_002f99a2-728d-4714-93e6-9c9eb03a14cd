'use client'

import { useTranslations } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { RotateCcw, Calendar, Package, Mail } from 'lucide-react'

export default function ReturnsPage() {
  const t = useTranslations('shipping.returns')

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-6">
            <RotateCcw className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {t('withdrawalRight')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('withdrawalDescription')}
              </p>
              <p>
                {t('withdrawalPeriod')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('declareWithdrawal')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('declareWithdrawalDescription')}
              </p>
              <div className="bg-muted p-4 rounded-lg mt-4">
                <p className="font-semibold">{t('contactForWithdrawal')}</p>
                <p>
                  PrimeCaffe AG<br />
                  Musterstrasse 123<br />
                  8001 Zürich<br />
                  Schweiz
                </p>
                <p className="mt-2">
                  <strong>{t('email')}:</strong> <EMAIL><br />
                  <strong>{t('phone')}:</strong> +41 44 123 45 67
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {t('returnGoods')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <h4>{t('returnConditions')}</h4>
              <ul>
                <li>{t('returnCondition1')}</li>
                <li>{t('returnCondition2')}</li>
                <li>{t('returnCondition3')}</li>
              </ul>

              <h4>{t('returnCosts')}</h4>
              <p>
                {t('returnCostsDescription')}
              </p>

              <h4>{t('returnAddress')}</h4>
              <div className="bg-muted p-4 rounded-lg">
                <p>
                  PrimeCaffe AG<br />
                  Retouren-Abteilung<br />
                  Musterstrasse 123<br />
                  8001 Zürich<br />
                  Schweiz
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('refund')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('refundDescription')}
              </p>
              <p>
                {t('refundMethod')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('exchange')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('exchangeDescription')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                {t('questionsAboutReturns')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                {t('questionsDescription')}
              </p>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{t('email')}</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
                <div>
                  <p className="font-medium">{t('phone')}</p>
                  <p className="text-sm text-muted-foreground">+41 44 123 45 67</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
