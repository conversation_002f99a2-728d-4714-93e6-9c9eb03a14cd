import { createClient } from '@/lib/supabase/server'
import { getTranslations } from 'next-intl/server'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { formatCurrency } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'

export const dynamic = 'force-dynamic'

interface OrderDetailPageProps {
  params: Promise<{ locale: string; id: string }>
}

function getStatusColor(status: string) {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'processing':
      return 'bg-blue-100 text-blue-800'
    case 'shipped':
      return 'bg-purple-100 text-purple-800'
    case 'delivered':
      return 'bg-green-100 text-green-800'
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export default async function OrderDetailPage({ params }: OrderDetailPageProps) {
  const { locale, id } = await params
  const supabase = await createClient()

  const { data: { user } } = await supabase.auth.getUser()

  const { data: order, error } = await supabase
    .from('orders')
    .select(`
      *,
      order_items (
        quantity,
        total_price,
        products (
          id,
          title
        )
      )
    `)
    .eq('id', id)
    .single()

  if (error || !order) {
    notFound()
  }

  if (order.user_id && user?.id !== order.user_id) {
    notFound()
  }

  const tAccount = await getTranslations({ locale, namespace: 'account' })
  const tCheckout = await getTranslations({ locale, namespace: 'checkout.success' })

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/${locale}/account`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {tAccount('orders.title')}
          </Link>
        </Button>

        <Card>
          <CardHeader>
            <CardTitle>{tCheckout('orderDetails')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-muted p-4 rounded-lg">
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>{tCheckout('orderNumber')}:</span>
                  <span className="font-mono">#{order.order_number || order.id.slice(-8)}</span>
                </div>
                <div className="flex justify-between">
                  <span>{tCheckout('date')}:</span>
                  <span>{new Date(order.created_at).toLocaleDateString(locale === 'it' ? 'it-CH' : locale === 'fr' ? 'fr-CH' : 'de-CH')}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>{tCheckout('status')}:</span>
                  <Badge className={`${getStatusColor(order.status)} w-fit`}>
                    {tAccount(`orders.statuses.${order.status}`)}
                  </Badge>
                </div>
                {order.tracking_number && (
                  <div className="flex justify-between">
                    <span>{tAccount('orders.trackingNumber')}:</span>
                    <span className="font-mono text-sm">{order.tracking_number}</span>
                  </div>
                )}
                <div className="flex justify-between font-semibold">
                  <span>{tCheckout('totalAmount')}:</span>
                  <span>{formatCurrency(order.total_amount)}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-3">{tCheckout('orderedItems')}</h3>
              <div className="space-y-2">
                {order.order_items.map((item: { products: { title: string }; quantity: number; total_price: number }, index: number) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b">
                    <div>
                      <p className="font-medium">{item.products.title}</p>
                      <p className="text-sm text-muted-foreground">{tCheckout('quantity')}: {item.quantity}</p>
                    </div>
                    <p className="font-medium">{formatCurrency(item.total_price)}</p>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
