import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import BundleManagementContent from '@/components/admin/bundle-management-content'

interface AdminBundlesProps {
  params: Promise<{ locale: string }>;
}

export default async function AdminBundlesPage({ params }: AdminBundlesProps) {
  const { locale } = await params;
  const supabase = await createClient()

  // Check if user is authenticated and is admin
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect(`/${locale}/login`)
  }

  // Check admin status
  const { data: profile } = await supabase
    .from('users')
    .select('is_admin')
    .eq('id', user.id)
    .single()

  if (!profile?.is_admin) {
    redirect(`/${locale}`)
  }

  // Get all bundles
  const { data: bundles, error } = await supabase
    .from('bundles')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching bundles:', error)
  }

  return <BundleManagementContent bundles={bundles || []} locale={locale} />
}
