import { createClient } from '@/lib/supabase/client'
import { safeGetUser } from '@/lib/supabase/helpers'
import { useState, useEffect, useCallback } from 'react'
import {
  getClientCartSessionId,
  clearClientCartSession,
} from '@/lib/cart-session'

export interface CartItem {
  id: string
  product_id?: string
  bundle_id?: string
  quantity: number
  product?: {
    id: string
    title: string
    price: number
    discount_price?: number
    images?: string[]
    category: string
    type?: string
    brand?: string
  }
  bundle?: {
    id: string
    title: string
    total_price: number
    discount_price?: number
    image?: string
  }
}

export interface Cart {
  id: string
  user_id?: string
  session_id?: string
  items: CartItem[]
  total_amount: number
  created_at: string
  updated_at: string
}

// Simplified CartManager
export class CartManager {
  private supabase = createClient()

  async getCart(userId?: string): Promise<Cart | null> {
    try {
      if (!userId) {
        const sessionId = this.getSessionId()
        return await this.getCartBySessionId(sessionId)
      } else {
        // For authenticated users, look for user carts first
        let cart = await this.getCartByUserId(userId)
        
        // If no user cart found, check for session cart
        if (!cart) {
          const sessionId = this.getSessionId()
          cart = await this.getCartBySessionId(sessionId)
        }
        
        return cart
      }
    } catch (error) {
      console.error('🛒 CartManager.getCart: Error:', error)
      return null
    }
  }

  private async getCartByUserId(userId: string): Promise<Cart | null> {
    try {
      const { data: cartData, error } = await this.supabase
        .from('carts')
        .select(`
          *,
          cart_items (
            *,
            products (
              id,
              title,
              price,
              discount_price,
              images,
              category,
              coffee_type,
              brand
            ),
            bundles (
              id,
              title,
              total_price,
              discount_price,
              image
            )
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('updated_at', { ascending: false })
        .limit(1)
        .maybeSingle()

      if (error && error.code !== 'PGRST116') {
        console.error('🛒 CartManager.getCartByUserId: Database error:', error)
        return null
      }

      if (!cartData) return null

      return this.transformCartData(cartData)
    } catch (error) {
      console.error('🛒 CartManager.getCartByUserId: Error:', error)
      return null
    }
  }

  private async getCartBySessionId(sessionId: string): Promise<Cart | null> {
    try {
      const { data: cartData, error } = await this.supabase
        .from('carts')
        .select(`
          *,
          cart_items (
            *,
            products (
              id,
              title,
              price,
              discount_price,
              images,
              category,
              coffee_type,
              brand
            ),
            bundles (
              id,
              title,
              total_price,
              discount_price,
              image
            )
          )
        `)
        .eq('session_id', sessionId)
        .is('user_id', null)
        .eq('status', 'active')
        .order('updated_at', { ascending: false })
        .limit(1)
        .maybeSingle()

      if (error && error.code !== 'PGRST116') {
        console.error('🛒 CartManager.getCartBySessionId: Database error:', error)
        return null
      }

      if (!cartData) return null

      return this.transformCartData(cartData)
    } catch (error) {
      console.error('🛒 CartManager.getCartBySessionId: Error:', error)
      return null
    }
  }

  private transformCartData(data: {
    id: string
    user_id?: string
    session_id?: string
    total_amount: number
    created_at: string
    updated_at: string
    cart_items?: Array<{
      id: string
      product_id?: string
      bundle_id?: string
      quantity: number
      products?: {
        id: string
        title: string
        price: number
        discount_price?: number
        images?: string[]
        category: string
        coffee_type?: string
        brand?: string
      }
      bundles?: {
        id: string
        title: string
        total_price: number
        discount_price?: number
        image?: string
      }
    }>
  }): Cart {
    const cart: Cart = {
      ...data,
      items: data.cart_items?.map((item) => ({
        ...item,
        product: item.products ? {
          ...item.products,
          type: item.products.coffee_type
        } : undefined,
        bundle: item.bundles ? {
          ...item.bundles
        } : undefined
      })) || []
    }

    return cart
  }

  async addToCart(productId: string, quantity: number = 1, userId?: string, isBundle: boolean = false): Promise<boolean> {
    try {
      let cart = await this.getCart(userId)

      if (!cart) {
        // Create new cart
        const cartData: {
          status: string
          total_amount: number
          user_id?: string
          session_id?: string
        } = {
          status: 'active',
          total_amount: 0
        }

        if (userId) {
          cartData.user_id = userId
        } else {
          cartData.session_id = this.getSessionId()
        }

        const { data: newCart, error: cartError } = await this.supabase
          .from('carts')
          .insert(cartData)
          .select()
          .single()

        if (cartError) {
          console.error('Error creating cart:', cartError)
          return false
        }

        cart = { ...newCart, items: [] }
      }

      if (!cart) {
        console.error('Cart is null after creation')
        return false
      }

      // Check if item already exists in cart
      const existingItem = isBundle
        ? cart.items.find(item => item.bundle_id === productId)
        : cart.items.find(item => item.product_id === productId)

      if (existingItem) {
        // Update existing item quantity
        const { error } = await this.supabase
          .from('cart_items')
          .update({ quantity: existingItem.quantity + quantity })
          .eq('id', existingItem.id)

        if (error) {
          console.error('Error updating cart item:', error)
          return false
        }
      } else {
        // Add new item to cart
        const insertData: {
          cart_id: string
          quantity: number
          product_id?: string
          bundle_id?: string
        } = {
          cart_id: cart.id,
          quantity
        }

        if (isBundle) {
          insertData.bundle_id = productId
        } else {
          insertData.product_id = productId
        }

        const { error } = await this.supabase
          .from('cart_items')
          .insert(insertData)

        if (error) {
          console.error('Error adding cart item:', error)
          return false
        }
      }

      // Update cart total
      await this.updateCartTotal(cart.id)
      return true
    } catch (error) {
      console.error('Error in addToCart:', error)
      return false
    }
  }

  async updateCartItem(itemId: string, quantity: number): Promise<boolean> {
    try {
      if (quantity <= 0) {
        return await this.removeFromCart(itemId)
      }

      const { error } = await this.supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', itemId)

      if (error) {
        console.error('Error updating cart item:', error)
        return false
      }

      // Get cart_id to update total
      const { data: item } = await this.supabase
        .from('cart_items')
        .select('cart_id')
        .eq('id', itemId)
        .maybeSingle()

      if (item) {
        await this.updateCartTotal(item.cart_id)
      }

      return true
    } catch (error) {
      console.error('Error in updateCartItem:', error)
      return false
    }
  }

  async removeFromCart(itemId: string): Promise<boolean> {
    try {
      // Get cart_id before deleting
      const { data: item } = await this.supabase
        .from('cart_items')
        .select('cart_id')
        .eq('id', itemId)
        .maybeSingle()

      const { error } = await this.supabase
        .from('cart_items')
        .delete()
        .eq('id', itemId)

      if (error) {
        console.error('Error removing cart item:', error)
        return false
      }

      if (item) {
        await this.updateCartTotal(item.cart_id)
      }

      return true
    } catch (error) {
      console.error('Error in removeFromCart:', error)
      return false
    }
  }

  async clearCart(): Promise<boolean> {
    try {
      const user = await safeGetUser(this.supabase)
      const cart = await this.getCart(user?.id)

      if (!cart) return true

      const { error } = await this.supabase
        .from('cart_items')
        .delete()
        .eq('cart_id', cart.id)

      if (error) {
        console.error('Error clearing cart:', error)
        return false
      }

      await this.updateCartTotal(cart.id)
      return true
    } catch (error) {
      console.error('Error in clearCart:', error)
      return false
    }
  }

  private async updateCartTotal(cartId: string): Promise<void> {
    try {
      const { data: items } = await this.supabase
        .from('cart_items')
        .select(`
          quantity,
          products (price, discount_price),
          bundles (total_price, discount_price)
        `)
        .eq('cart_id', cartId)

      const total = items?.reduce((sum, item) => {
        const product = item.products as { price?: number; discount_price?: number } | undefined
        const bundle = item.bundles as { total_price?: number; discount_price?: number } | undefined

        let price = 0
        if (product) {
          price = product.discount_price || product.price || 0
        } else if (bundle) {
          price = bundle.discount_price || bundle.total_price || 0
        }

        return sum + (price * item.quantity)
      }, 0) || 0

      await this.supabase
        .from('carts')
        .update({ total_amount: total })
        .eq('id', cartId)
    } catch (error) {
      console.error('Error updating cart total:', error)
    }
  }

  private getSessionId(): string {
    return getClientCartSessionId()
  }

  async clearSession(): Promise<void> {
    clearClientCartSession()
  }
}

// Singleton CartManager instance
let cartManagerInstance: CartManager | null = null

export function getCartManager(): CartManager {
  if (!cartManagerInstance) {
    cartManagerInstance = new CartManager()
  }
  return cartManagerInstance
}

// React hook for cart management
export function useCart() {
  const [cart, setCart] = useState<Cart>({
    id: '',
    items: [],
    total_amount: 0,
    created_at: '',
    updated_at: ''
  })
  const [loading, setLoading] = useState(true)
  const cartManager = getCartManager()

  const refreshCart = useCallback(async () => {
    setLoading(true)
    try {
      const supabase = createClient()
      const user = await safeGetUser(supabase)
      const cartData = await cartManager.getCart(user?.id)

      if (cartData) {
        setCart(cartData)
      } else {
        setCart({
          id: '',
          items: [],
          total_amount: 0,
          created_at: '',
          updated_at: ''
        })
      }
    } catch (error) {
      console.error('🛒 useCart: Error refreshing cart:', error)
      setCart({
        id: '',
        items: [],
        total_amount: 0,
        created_at: '',
        updated_at: ''
      })
    } finally {
      setLoading(false)
    }
  }, [cartManager])

  useEffect(() => {
    refreshCart()

    // Listen for cart updates from other components
    const handleCartUpdate = () => {
      refreshCart()
    }

    window.addEventListener('cartUpdated', handleCartUpdate)

    return () => {
      window.removeEventListener('cartUpdated', handleCartUpdate)
    }
  }, [refreshCart])

  const addToCart = async (productId: string, quantity: number = 1) => {
    const success = await cartManager.addToCart(productId, quantity)
    if (success) {
      await refreshCart()
    }
    return success
  }

  const updateCartItem = async (itemId: string, quantity: number) => {
    const success = await cartManager.updateCartItem(itemId, quantity)
    if (success) {
      // Trigger refresh for all cart instances
      window.dispatchEvent(new CustomEvent('cartUpdated'))
    }
    return success
  }

  const removeFromCart = async (itemId: string) => {
    const success = await cartManager.removeFromCart(itemId)
    if (success) {
      // Trigger refresh for all cart instances
      window.dispatchEvent(new CustomEvent('cartUpdated'))
    }
    return success
  }

  const clearCart = async () => {
    const success = await cartManager.clearCart()
    if (success) {
      await refreshCart()
    }
    return success
  }

  return {
    cart,
    loading,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refreshCart
  }
}
