'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'

interface Level {
  id?: string
  level: number
  name: string
  minimum_points: number
  discount_percentage: number
  points_multiplier: number
}

interface LevelFormProps {
  locale: string
  level?: Level
}

export default function LevelForm({ locale, level }: LevelFormProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<Level>({
    id: level?.id,
    level: level?.level || 1,
    name: level?.name || '',
    minimum_points: level?.minimum_points || 0,
    discount_percentage: level?.discount_percentage || 0,
    points_multiplier: level?.points_multiplier || 1,
  })

  const { toast } = useToast()
  const router = useRouter()
  const t = useTranslations('admin')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    const method = level ? 'PATCH' : 'POST'
    const url = level
      ? `/api/admin/gamification/levels/${level.id}`
      : '/api/admin/gamification/levels'
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })
      if (response.ok) {
        toast({
          title: t('levels.form.savedTitle'),
          description: t('levels.form.savedMessage'),
        })
        router.push(`/${locale}/admin/gamification`)
      } else {
        const err = await response.json()
        throw new Error(err.error || 'Error')
      }
    } catch (err) {
      console.error('Error saving level:', err)
      toast({
        title: t('levels.form.saveErrorTitle'),
        description: t('levels.form.saveErrorMessage'),
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="level">{t('levels.form.levelLabel')} *</Label>
            <Input
              id="level"
              type="number"
              value={formData.level}
              onChange={(e) => setFormData({ ...formData, level: parseInt(e.target.value) })}
              required
            />
          </div>
          <div>
            <Label htmlFor="name">{t('levels.form.nameLabel')} *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>
          <div>
            <Label htmlFor="minimum_points">{t('levels.form.minimumPointsLabel')} *</Label>
            <Input
              id="minimum_points"
              type="number"
              value={formData.minimum_points}
              onChange={(e) => setFormData({ ...formData, minimum_points: parseInt(e.target.value) })}
              required
            />
          </div>
        </div>
        <div className="space-y-4">
          <div>
            <Label htmlFor="discount_percentage">{t('levels.form.discountLabel')} *</Label>
            <Input
              id="discount_percentage"
              type="number"
              value={formData.discount_percentage}
              onChange={(e) => setFormData({ ...formData, discount_percentage: parseFloat(e.target.value) })}
              required
            />
          </div>
          <div>
            <Label htmlFor="points_multiplier">{t('levels.form.pointsMultiplierLabel')} *</Label>
            <Input
              id="points_multiplier"
              type="number"
              value={formData.points_multiplier}
              onChange={(e) => setFormData({ ...formData, points_multiplier: parseFloat(e.target.value) })}
              step="0.1"
              required
            />
          </div>
        </div>
      </div>
      <Button type="submit" disabled={loading}>
        {loading ? t('levels.form.saving') : t('levels.form.save')}
      </Button>
    </form>
  )
}

