'use client'

import { useState, useMemo } from 'react'
import { useTranslations } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from '@/components/ui/alert-dialog'
import ProductTemplateSelector from '@/components/admin/product-template-selector'
import { Settings, CheckSquare } from 'lucide-react'

interface Product {
  id: string
  title: string
  category: string
  coffee_type?: string
  brand?: string
  blend?: string
  is_available: boolean
}

interface ProductTemplate {
  id: string
  name: string
  description: string
  title_template: string
  category: 'coffee' | 'accessories'
  coffee_type?: string
  brand?: string
  blend?: string
  machine_compatibility?: string[]
  pack_quantity?: number
  pack_weight_grams?: number
  price?: number
  discount_price?: number
  cost_per_espresso?: number
  inventory_count?: number
  purchase_cost?: number
  is_available?: boolean
  is_default: boolean
}

interface ProductBulkActionsProps {
  products: Product[]
  onProductsUpdate: () => void
}

export default function ProductBulkActions({ products, onProductsUpdate }: ProductBulkActionsProps) {
  const t = useTranslations('admin.products')
  const tCommon = useTranslations('admin.common')
  const { toast } = useToast()
  const supabase = useMemo(() => createClient(), [])

  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<ProductTemplate | null>(null)
  const [loading, setLoading] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  const handleProductSelect = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId])
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map(p => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  const applyTemplateToProducts = async () => {
    if (!selectedTemplate || selectedProducts.length === 0) return

    setLoading(true)
    try {
      // Apply template to each selected product
      const updates = selectedProducts.map(productId => {
        const product = products.find(p => p.id === productId)
        if (!product) return null

        return {
          id: productId,
          title_template: selectedTemplate.title_template,
          template_id: selectedTemplate.id,
          // Apply template values if they exist
          ...(selectedTemplate.category && { category: selectedTemplate.category }),
          ...(selectedTemplate.coffee_type && { coffee_type: selectedTemplate.coffee_type }),
          ...(selectedTemplate.brand && { brand: selectedTemplate.brand }),
          ...(selectedTemplate.blend && { blend: selectedTemplate.blend }),
          ...(selectedTemplate.machine_compatibility && { machine_compatibility: selectedTemplate.machine_compatibility }),
          ...(selectedTemplate.pack_quantity && { pack_quantity: selectedTemplate.pack_quantity }),
          ...(selectedTemplate.pack_weight_grams && { pack_weight_grams: selectedTemplate.pack_weight_grams }),
          ...(selectedTemplate.price && { price: selectedTemplate.price }),
          ...(selectedTemplate.discount_price && { discount_price: selectedTemplate.discount_price }),
          ...(selectedTemplate.cost_per_espresso && { cost_per_espresso: selectedTemplate.cost_per_espresso }),
          ...(selectedTemplate.inventory_count !== undefined && { inventory_count: selectedTemplate.inventory_count }),
          ...(selectedTemplate.purchase_cost && { purchase_cost: selectedTemplate.purchase_cost }),
          ...(selectedTemplate.is_available !== undefined && { is_available: selectedTemplate.is_available })
        }
      }).filter(Boolean)

      // Update products in batches
      const batchSize = 10
      for (let i = 0; i < updates.length; i += batchSize) {
        const batch = updates.slice(i, i + batchSize)
        
        const { error } = await supabase
          .from('products')
          .upsert(batch, { onConflict: 'id' })

        if (error) {
          console.error('Error updating products batch:', error)
          throw error
        }
      }

      toast({
        title: t('templates.bulkApplySuccess'),
        description: t('templates.bulkApplySuccess', { count: selectedProducts.length })
      })

      setSelectedProducts([])
      setSelectedTemplate(null)
      setShowConfirmDialog(false)
      onProductsUpdate()

    } catch (error) {
      console.error('Error applying template to products:', error)
      toast({
        title: t('templates.bulkApplyError'),
        description: error instanceof Error ? error.message : t('templates.bulkApplyError'),
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const allSelected = products.length > 0 && selectedProducts.length === products.length
  const someSelected = selectedProducts.length > 0 && selectedProducts.length < products.length

  return (
    <div className="space-y-6">
      {/* Template Selection */}
      <ProductTemplateSelector
        selectedTemplate={selectedTemplate}
        onTemplateSelect={setSelectedTemplate}
        onTemplateApply={() => {}} // We handle this differently for bulk actions
        showPreview={false}
      />

      {/* Product Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <CheckSquare className="h-5 w-5" />
              {t('templates.selectProducts')}
            </span>
            <div className="flex items-center gap-4">
              <Badge variant="secondary">
                {t('templates.productsSelected', { count: selectedProducts.length })}
              </Badge>
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={allSelected || someSelected}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm">
                  {allSelected ? tCommon('deselectAll') : tCommon('selectAll')}
                  {someSelected && !allSelected && ` (${selectedProducts.length})`}
                </span>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {products.map((product) => (
              <div
                key={product.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedProducts.includes(product.id)
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => handleProductSelect(product.id, !selectedProducts.includes(product.id))}
              >
                <div className="flex items-start gap-3">
                  <Checkbox
                    checked={selectedProducts.includes(product.id)}
                    onCheckedChange={(checked) => handleProductSelect(product.id, !!checked)}
                    onClick={(e) => e.stopPropagation()}
                  />
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm truncate">{product.title}</h4>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {product.category === 'coffee' ? t('categories.coffee') : t('categories.accessories')}
                      </Badge>
                      {product.coffee_type && (
                        <Badge variant="secondary" className="text-xs">
                          {product.coffee_type}
                        </Badge>
                      )}
                    </div>
                    {product.brand && (
                      <p className="text-xs text-muted-foreground mt-1">{product.brand}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && selectedTemplate && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {t('templates.bulkActions')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">{t('templates.applyTemplateToProducts')}</p>
                <p className="text-sm text-muted-foreground">
                  {t('templates.productsSelected', { count: selectedProducts.length })} • {selectedTemplate.name}
                </p>
              </div>
              <Button
                onClick={() => setShowConfirmDialog(true)}
                disabled={loading}
              >
                <Settings className="mr-2 h-4 w-4" />
                {t('templates.applyToSelected')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('templates.applyTemplateToProducts')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('templates.confirmBulkApply', { count: selectedProducts.length })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={applyTemplateToProducts} disabled={loading}>
              {loading ? t('common.loading') : t('templates.applyTemplate')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
