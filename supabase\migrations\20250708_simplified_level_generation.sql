-- Migration: Simplified Level Generation System
-- Date: 2025-07-08
-- Description: Create a function to automatically generate levels based on league configuration

-- Create function to generate levels automatically
CREATE OR REPLACE FUNCTION generate_levels_from_config()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    config_row league_config%ROWTYPE;
    league_names TEXT[] := ARRAY['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond'];
    level_numbers TEXT[] := ARRAY['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII', 'XIII', 'XIV', 'XV', 'XVI', 'XVII', 'XVIII', 'XIX', 'XX'];
    league_points INTEGER[];
    league_discounts DECIMAL[];
    start_points INTEGER;
    end_points INTEGER;
    points_per_level INTEGER;
    global_level INTEGER;
    level_points INTEGER;
    league_idx INTEGER;
    level_idx INTEGER;
BEGIN
    -- Get active league configuration
    SELECT * INTO config_row 
    FROM league_config 
    WHERE is_active = true 
    LIMIT 1;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No active league configuration found';
    END IF;
    
    -- Prepare league data
    league_points := ARRAY[
        config_row.league_1_points,
        config_row.league_2_points,
        config_row.league_3_points,
        config_row.league_4_points,
        config_row.league_5_points
    ];
    
    league_discounts := ARRAY[
        config_row.league_1_discount,
        config_row.league_2_discount,
        config_row.league_3_discount,
        config_row.league_4_discount,
        config_row.league_5_discount
    ];
    
    -- Clear existing levels
    DELETE FROM user_levels;
    
    -- Generate levels for each league
    FOR league_idx IN 1..5 LOOP
        start_points := league_points[league_idx];
        
        -- Calculate end points (next league start or +5000 for last league)
        IF league_idx < 5 THEN
            end_points := league_points[league_idx + 1];
        ELSE
            end_points := start_points + 5000;
        END IF;
        
        -- Calculate points per level in this league
        points_per_level := (end_points - start_points) / config_row.levels_per_league;
        
        -- Generate levels for this league
        FOR level_idx IN 1..config_row.levels_per_league LOOP
            global_level := (league_idx - 1) * config_row.levels_per_league + level_idx;
            level_points := start_points + ((level_idx - 1) * points_per_level);
            
            INSERT INTO user_levels (
                level,
                name,
                minimum_points,
                discount_percentage,
                points_multiplier
            ) VALUES (
                global_level,
                league_names[league_idx] || ' ' || level_numbers[level_idx],
                level_points,
                league_discounts[league_idx],
                1.0
            );
        END LOOP;
    END LOOP;
    
    RAISE NOTICE 'Generated % levels successfully', config_row.levels_per_league * 5;
END;
$$;

-- Test the function with current configuration
SELECT generate_levels_from_config();

-- Add comment
COMMENT ON FUNCTION generate_levels_from_config() IS 'Automatically generates user levels based on league configuration. Clears existing levels and creates new ones with proper point distribution.';
