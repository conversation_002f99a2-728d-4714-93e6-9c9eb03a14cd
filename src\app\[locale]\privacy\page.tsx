"use client";

import { useTranslations, useLocale } from 'next-intl';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Mail, Phone, MapPin } from 'lucide-react';

export default function PrivacyPage() {
  const locale = useLocale();
  const t = useTranslations('legal.privacy');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
            <Shield className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-muted-foreground">
            {t('subtitle')}
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            {t('lastUpdated')}: {new Date().toLocaleDateString(locale === 'de' ? 'de-CH' : locale === 'fr' ? 'fr-CH' : 'it-CH')}
          </p>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>1. {t('responsibleEntity.title')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('responsibleEntity.description')}
              </p>
              <div className="bg-muted p-4 rounded-lg mt-4">
                <p className="font-semibold">{t('responsibleEntity.company')}</p>
                <p style={{ whiteSpace: 'pre-line' }}>{t('responsibleEntity.address')}</p>
                <p className="mt-2">
                  <strong>E-Mail:</strong> {t('responsibleEntity.email')}<br />
                  <strong>Telefon:</strong> {t('responsibleEntity.phone')}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>2. {t('dataCollection.title')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <h4>2.1 {t('dataCollection.websiteVisit.title')}</h4>
              <p>
                {t('dataCollection.websiteVisit.description')}
              </p>
              <ul>
                {t.raw('dataCollection.websiteVisit.items').map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
              
              <h4>2.2 {t('dataCollection.registration.title')}</h4>
              <p>
                {t('dataCollection.registration.description')}
              </p>
              <ul>
                {t.raw('dataCollection.registration.items').map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>

              <h4>2.3 {t('dataCollection.orders.title')}</h4>
              <p>
                {t('dataCollection.orders.description')}
              </p>
              <ul>
                {t.raw('dataCollection.orders.items').map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>3. {t('purpose.title')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>{t('purpose.description')}</p>
              <ul>
                {t.raw('purpose.items').map((item: string, index: number) => (
                  <li key={index}><strong>{item.split(':')[0]}:</strong> {item.split(':')[1]}</li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>4. {t('cookiesTracking.title')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <h4>4.1 {t('cookiesTracking.necessary.title')}</h4>
              <p>
                {t('cookiesTracking.necessary.description')}
              </p>
              <ul>
                {t.raw('cookiesTracking.necessary.items').map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>

              <h4>4.2 {t('cookiesTracking.analytics.title')}</h4>
              <p>
                {t('cookiesTracking.analytics.description')}
              </p>
              <ul>
                {t.raw('cookiesTracking.analytics.items').map((item: string, index: number) => (
                  <li key={index}><strong>{item.split(':')[0]}:</strong> {item.split(':')[1]}</li>
                ))}
              </ul>
              
              <p>
                {t('cookiesTracking.settings')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>5. {t('rights.title')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>{t('rights.description')}</p>
              <ul>
                {t.raw('rights.items').map((item: string, index: number) => (
                  <li key={index}><strong>{item.split(':')[0]}:</strong> {item.split(':')[1]}</li>
                ))}
              </ul>
              
              <p>
                {t('rights.contact')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>6. {t('contact.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                {t('contact.description')}
              </p>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">E-Mail</p>
                    <p className="text-sm text-muted-foreground">{t('contact.email')}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Telefon</p>
                    <p className="text-sm text-muted-foreground">{t('contact.phone')}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Adresse</p>
                    <p className="text-sm text-muted-foreground">{t('contact.address')}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
