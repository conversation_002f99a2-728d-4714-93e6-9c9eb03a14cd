'use client'

import { useEffect, useMemo } from 'react'
import { NextIntlClientProvider, useTranslations } from 'next-intl'
import { usePathname } from 'next/navigation'
import { locales, defaultLocale } from '@/lib/i18n/config'
import de from '@/messages/de.json'
import fr from '@/messages/fr.json'
import it from '@/messages/it.json'

interface GlobalErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  const pathname = usePathname()
  const locale = useMemo(() => {
    if (!pathname) return defaultLocale
    const match = locales.find(l => pathname.startsWith(`/${l}/`))
    return match || defaultLocale
  }, [pathname])

  const messages = useMemo(() => {
    switch (locale) {
      case 'fr':
        return fr
      case 'it':
        return it
      default:
        return de
    }
  }, [locale])

  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global application error:', error)
  }, [error])

  return (
    <html lang={locale}>
      <body className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
        <NextIntlClientProvider locale={locale} messages={messages}>
          <GlobalErrorContent error={error} reset={reset} />
        </NextIntlClientProvider>
      </body>
    </html>
  )
}

function GlobalErrorContent({ error, reset }: GlobalErrorProps) {
  const t = useTranslations('globalError')

  return (
    <div className="max-w-2xl mx-auto text-center">
      <div className="bg-white rounded-2xl shadow-2xl border-2 border-red-200 p-12">
        {/* Critical Error Animation */}
        <div className="relative mb-8">
          <div className="flex justify-center items-center space-x-4">
            <div className="relative">
              <div className="w-24 h-24 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                <span className="text-4xl text-white">☕</span>
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-600 rounded-full flex items-center justify-center animate-bounce">
                <span className="text-white text-xl">!</span>
              </div>
            </div>
          </div>

          {/* Critical error indicators */}
          <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div className="flex space-x-2">
              <div className="w-2 h-12 bg-gradient-to-t from-red-500 to-transparent rounded-full animate-pulse"></div>
              <div className="w-2 h-8 bg-gradient-to-t from-red-500 to-transparent rounded-full animate-pulse delay-75"></div>
              <div className="w-2 h-16 bg-gradient-to-t from-red-500 to-transparent rounded-full animate-pulse delay-150"></div>
            </div>
          </div>
        </div>

        {/* Critical Error Message */}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          {t('title')}
        </h1>
        <p className="text-xl text-red-600 font-semibold mb-6">
          {t('subtitle')}
        </p>

        {/* Error Description */}
        <div className="bg-red-50 rounded-lg p-6 mb-8 border border-red-200">
          <p className="text-gray-700 mb-4">
            {t('description')}
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-red-700">
            <span>⚡</span>
            <span>{t('emergencyMode')}</span>
          </div>
        </div>

        {/* Emergency Actions */}
        <div className="space-y-4">
          <button
            onClick={reset}
            className="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <span>🔄</span>
            <span>{t('emergencyRestart')}</span>
          </button>

          <button
            onClick={() => window.location.href = '/'}
            className="w-full bg-amber-600 hover:bg-amber-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <span>🏠</span>
            <span>{t('emergencyHome')}</span>
          </button>

          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={() => window.location.reload()}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200"
            >
              {t('reloadPage')}
            </button>

            <button
              onClick={() => window.history.back()}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200"
            >
              {t('goBack')}
            </button>
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="mt-8 pt-6 border-t border-red-200">
          <p className="text-sm text-gray-600 mb-2">
            {t('contactInfo')}
          </p>
          <p className="text-sm font-mono text-gray-500">
            {t('supportEmail')}
          </p>
          <p className="text-xs text-gray-400 mt-4">
            {t('errorId')} {error.digest || t('defaultErrorId')}
          </p>
        </div>

        {/* Development Info */}
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-6 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 text-center">
              {t('devInfo')}
            </summary>
            <div className="mt-4 p-4 bg-gray-100 rounded text-xs font-mono text-gray-700 overflow-auto max-h-40">
              <p><strong>Error:</strong> {error.message}</p>
              {error.digest && <p><strong>Digest:</strong> {error.digest}</p>}
              {error.stack && (
                <details className="mt-2">
                  <summary className="cursor-pointer">{t('stackTrace')}</summary>
                  <pre className="mt-1 whitespace-pre-wrap text-xs">{error.stack}</pre>
                </details>
              )}
            </div>
          </details>
        )}
      </div>
    </div>
  )
}
