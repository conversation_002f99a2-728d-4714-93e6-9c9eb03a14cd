import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "PrimeCaffe - Premium Kaffee aus der Schweiz",
  description: "Entdecken Sie erstklassigen Kaffeegenuss mit PrimeCaffe. Premium Kaffeekapseln, Bohnen und Zubehör direkt aus der Schweiz.",
  keywords: "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Schweiz, Premium, Espresso",
  authors: [{ name: "PrimeCaffe" }],
  creator: "PrimeCaffe",
  publisher: "PrimeCaffe",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="de-CH">
      <body
        className="antialiased min-h-screen flex flex-col overflow-x-hidden"
      >
        {children}
      </body>
    </html>
  );
}
