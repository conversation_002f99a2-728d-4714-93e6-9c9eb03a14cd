'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { <PERSON><PERSON>, X, Settings, Shield, BarChart3, Target } from 'lucide-react';
import Link from 'next/link';

export function CookieBanner() {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const t = useTranslations('cookies');
  const locale = useLocale();

  useEffect(() => {
    // Check if user has already made a choice
    const cookieConsent = localStorage.getItem('cookie-consent');
    if (!cookieConsent) {
      setShowBanner(true);
      // Delay visibility for smooth animation
      setTimeout(() => setIsVisible(true), 100);
    }
  }, []);

  const acceptAll = () => {
    localStorage.setItem('cookie-consent', 'all');
    handleClose();
    // Enable all tracking here
    enableAnalytics();
  };

  const acceptNecessary = () => {
    localStorage.setItem('cookie-consent', 'necessary');
    handleClose();
    // Only enable necessary cookies
  };

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      setShowBanner(false);
      setShowSettings(false);
    }, 300); // Match animation duration
  };

  const saveSettings = (analytics: boolean, marketing: boolean) => {
    const settings = {
      necessary: true,
      analytics,
      marketing,
    };
    localStorage.setItem('cookie-consent', JSON.stringify(settings));
    handleClose();

    if (analytics) {
      enableAnalytics();
    }
    if (marketing) {
      enableMarketing();
    }
  };

  const enableAnalytics = () => {
    // Enable Google Analytics / GTM
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_GTM_ID) {
      // GTM implementation would go here
    }
  };

  const enableMarketing = () => {
    // Enable Meta Pixel
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_META_PIXEL_ID) {
      // Meta Pixel implementation would go here
    }
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Backdrop with blur effect */}
      <div
        className={`fixed inset-0 z-[100] transition-all duration-300 ease-out ${
          isVisible
            ? 'backdrop-blur-sm bg-black/20'
            : 'backdrop-blur-none bg-transparent pointer-events-none'
        }`}
        onClick={handleClose}
      />

      {/* Cookie Banner */}
      <div
        className={`fixed bottom-0 left-0 right-0 z-[101] p-4 sm:p-6 transition-all duration-300 ease-out ${
          isVisible
            ? 'translate-y-0 opacity-100'
            : 'translate-y-full opacity-0'
        }`}
      >
        <div className="max-w-5xl mx-auto">
          {/* Liquid Glass Container */}
          <div className="liquid-glass-container">
            {!showSettings ? (
              <CookieBannerContent
                t={t}
                locale={locale}
                onAcceptAll={acceptAll}
                onAcceptNecessary={acceptNecessary}
                onShowSettings={() => setShowSettings(true)}
                onClose={handleClose}
              />
            ) : (
              <CookieSettings
                onSave={saveSettings}
                onBack={() => setShowSettings(false)}
                t={t}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
}

// Cookie Banner Content Component
interface CookieBannerContentProps {
  t: (key: string) => string;
  locale: string;
  onAcceptAll: () => void;
  onAcceptNecessary: () => void;
  onShowSettings: () => void;
  onClose: () => void;
}

function CookieBannerContent({
  t,
  locale,
  onAcceptAll,
  onAcceptNecessary,
  onShowSettings,
  onClose
}: CookieBannerContentProps) {
  return (
    <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
      {/* Icon and Content */}
      <div className="flex items-start gap-4 flex-1">
        <div className="liquid-glass-icon">
          <Cookie className="h-6 w-6 text-white" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white mb-2">
            {t('title') || 'Cookie-Einstellungen'}
          </h3>
          <p className="text-sm text-white/80 leading-relaxed mb-3">
            {t('description') || 'Wir verwenden Cookies, um Ihre Erfahrung zu verbessern und unsere Website zu optimieren.'}
          </p>
          <Link
            href={`/${locale}/cookies`}
            className="text-sm text-white/90 hover:text-white transition-colors underline underline-offset-2"
          >
            {t('learnMore') || 'Mehr erfahren'}
          </Link>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto lg:min-w-[400px]">
        <button
          onClick={onShowSettings}
          className="liquid-glass-button liquid-glass-button-secondary"
        >
          <Settings className="mr-2 h-4 w-4" />
          {t('settings') || 'Einstellungen'}
        </button>
        <button
          onClick={onAcceptNecessary}
          className="liquid-glass-button liquid-glass-button-secondary"
        >
          {t('acceptNecessary') || 'Nur notwendige'}
        </button>
        <button
          onClick={onAcceptAll}
          className="liquid-glass-button liquid-glass-button-primary"
        >
          {t('acceptAll') || 'Alle akzeptieren'}
        </button>
      </div>

      {/* Close Button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
        aria-label="Schließen"
      >
        <X className="h-4 w-4 text-white" />
      </button>
    </div>
  );
}

// Cookie Settings Component
interface CookieSettingsProps {
  onSave: (analytics: boolean, marketing: boolean) => void;
  onBack: () => void;
  t: (key: string) => string;
}

function CookieSettings({ onSave, onBack, t }: CookieSettingsProps) {
  const [analytics, setAnalytics] = useState(false);
  const [marketing, setMarketing] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">
          {t('settingsTitle') || 'Cookie-Einstellungen'}
        </h3>
        <div className="flex gap-2">
          <button
            onClick={onBack}
            className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
            aria-label="Zurück"
          >
            <X className="h-4 w-4 text-white" />
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {/* Necessary Cookies */}
        <div className="liquid-glass-card">
          <div className="flex items-start gap-4">
            <div className="liquid-glass-icon-small">
              <Shield className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-white mb-1">
                {t('necessary.title') || 'Notwendige Cookies'}
              </h4>
              <p className="text-sm text-white/70">
                {t('necessary.description') || 'Diese Cookies sind für die Grundfunktionen der Website erforderlich.'}
              </p>
            </div>
            <div className="text-sm font-medium text-white/60 bg-white/10 px-3 py-1 rounded-full">
              {t('alwaysActive') || 'Immer aktiv'}
            </div>
          </div>
        </div>

        {/* Analytics Cookies */}
        <div className="liquid-glass-card">
          <div className="flex items-start gap-4">
            <div className="liquid-glass-icon-small">
              <BarChart3 className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-white mb-1">
                {t('analytics.title') || 'Analyse-Cookies'}
              </h4>
              <p className="text-sm text-white/70">
                {t('analytics.description') || 'Helfen uns zu verstehen, wie Besucher mit der Website interagieren.'}
              </p>
            </div>
            <LiquidGlassToggle
              checked={analytics}
              onChange={setAnalytics}
              aria-label="Analytics Cookies"
            />
          </div>
        </div>

        {/* Marketing Cookies */}
        <div className="liquid-glass-card">
          <div className="flex items-start gap-4">
            <div className="liquid-glass-icon-small">
              <Target className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-white mb-1">
                {t('marketing.title') || 'Marketing-Cookies'}
              </h4>
              <p className="text-sm text-white/70">
                {t('marketing.description') || 'Werden verwendet, um Ihnen relevante Werbung zu zeigen.'}
              </p>
            </div>
            <LiquidGlassToggle
              checked={marketing}
              onChange={setMarketing}
              aria-label="Marketing Cookies"
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-3 pt-2">
        <button
          onClick={() => onSave(false, false)}
          className="liquid-glass-button liquid-glass-button-secondary flex-1"
        >
          {t('acceptNecessary') || 'Nur notwendige'}
        </button>
        <button
          onClick={() => onSave(analytics, marketing)}
          className="liquid-glass-button liquid-glass-button-primary flex-1"
        >
          {t('saveSettings') || 'Einstellungen speichern'}
        </button>
      </div>
    </div>
  );
}

// Liquid Glass Toggle Component
interface LiquidGlassToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  'aria-label'?: string;
}

function LiquidGlassToggle({ checked, onChange, 'aria-label': ariaLabel }: LiquidGlassToggleProps) {
  return (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        className="sr-only peer"
        aria-label={ariaLabel}
      />
      <div className={`
        w-12 h-6 rounded-full transition-all duration-300 ease-out
        ${checked
          ? 'bg-gradient-to-r from-blue-400 to-purple-500 shadow-lg shadow-blue-500/25'
          : 'bg-white/20 border border-white/30'
        }
        peer-focus:ring-2 peer-focus:ring-white/50 peer-focus:ring-offset-2 peer-focus:ring-offset-transparent
      `}>
        <div className={`
          absolute top-0.5 left-0.5 w-5 h-5 rounded-full transition-all duration-300 ease-out
          ${checked
            ? 'translate-x-6 bg-white shadow-lg'
            : 'translate-x-0 bg-white/80'
          }
        `} />
      </div>
    </label>
  );
}
