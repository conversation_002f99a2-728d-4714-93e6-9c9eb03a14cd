'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'
import ImageUpload from '@/components/admin/image-upload'
import ProductTemplateSelector from '@/components/admin/product-template-selector'

interface ProductFormProps {
  locale: string
  product?: {
    id: string
    title: string
    description: string
    category: string
    coffee_type?: string
    brand?: string
    blend?: string
    machine_compatibility?: string[]
    pack_quantity?: number
    pack_weight_grams?: number
    price: number
    discount_price?: number
    cost_per_espresso?: number
    inventory_count: number
    purchase_cost?: number
    is_available: boolean
    images: string[]
    title_template?: string
    template_id?: string
  }
}

interface ProductTemplate {
  id: string
  name: string
  description: string
  title_template: string
  category: 'coffee' | 'accessories'
  coffee_type?: string
  brand?: string
  blend?: string
  machine_compatibility?: string[]
  pack_quantity?: number
  pack_weight_grams?: number
  price?: number
  discount_price?: number
  cost_per_espresso?: number
  inventory_count?: number
  purchase_cost?: number
  is_available?: boolean
  is_default: boolean
}

export default function ProductForm({ locale, product }: ProductFormProps) {
  const [loading, setLoading] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<ProductTemplate | null>(null)
  const [useTemplate, setUseTemplate] = useState(false)
  const [formData, setFormData] = useState({
    title: product?.title || '',
    description: product?.description || '',
    category: product?.category || 'coffee',
    coffee_type: product?.coffee_type || '',
    brand: product?.brand || '',
    blend: product?.blend || '',
    machine_compatibility: product?.machine_compatibility || [],
    pack_quantity: product?.pack_quantity || 0,
    pack_weight_grams: product?.pack_weight_grams || 0,
    price: product?.price || 0,
    discount_price: product?.discount_price || 0,
    cost_per_espresso: product?.cost_per_espresso || 0,
    inventory_count: product?.inventory_count || 0,
    purchase_cost: product?.purchase_cost || 0,
    is_available: product?.is_available ?? true,
    images: product?.images || [],
    title_template: product?.title_template || '',
    template_id: product?.template_id || ''
  })

  // Initialize template state if product has a template
  useEffect(() => {
    if (product?.title_template) {
      setUseTemplate(true)
    }
  }, [product])

  const { toast } = useToast()
  const router = useRouter()
  const t = useTranslations()

  // Template handling functions
  const handleTemplateSelect = (template: ProductTemplate | null) => {
    setSelectedTemplate(template)
  }

  const handleTemplateApply = (template: ProductTemplate) => {
    if (!template) return

    // Apply template values to form data
    const updatedFormData = { ...formData }

    if (template.category) updatedFormData.category = template.category
    if (template.coffee_type) updatedFormData.coffee_type = template.coffee_type
    if (template.brand) updatedFormData.brand = template.brand
    if (template.blend) updatedFormData.blend = template.blend
    if (template.machine_compatibility) updatedFormData.machine_compatibility = template.machine_compatibility
    if (template.pack_quantity) updatedFormData.pack_quantity = template.pack_quantity
    if (template.pack_weight_grams) updatedFormData.pack_weight_grams = template.pack_weight_grams
    if (template.price) updatedFormData.price = template.price
    if (template.discount_price) updatedFormData.discount_price = template.discount_price
    if (template.cost_per_espresso) updatedFormData.cost_per_espresso = template.cost_per_espresso
    if (template.inventory_count !== undefined) updatedFormData.inventory_count = template.inventory_count
    if (template.purchase_cost) updatedFormData.purchase_cost = template.purchase_cost
    if (template.is_available !== undefined) updatedFormData.is_available = template.is_available

    // Set template fields
    updatedFormData.title_template = template.title_template
    updatedFormData.template_id = template.id

    setFormData(updatedFormData)
    setUseTemplate(true)

    toast({
      title: t('admin.products.templates.applyTemplate'),
      description: t('admin.products.templates.templateCreated')
    })
  }

  // Automatically calculate cost per espresso when related fields change
  useEffect(() => {
    const { price, discount_price, coffee_type, pack_quantity, pack_weight_grams } = formData
    let cost = 0

    // Use discount price if available, otherwise use regular price
    const effectivePrice = discount_price && discount_price > 0 ? discount_price : price

    if ((coffee_type === 'capsules' || coffee_type === 'pods') && pack_quantity > 0) {
      cost = effectivePrice / pack_quantity
    } else if ((coffee_type === 'beans' || coffee_type === 'ground') && pack_weight_grams > 0) {
      const espressoCount = pack_weight_grams / 7.5
      cost = effectivePrice / espressoCount
    }

    cost = isFinite(cost) && cost > 0 ? parseFloat(cost.toFixed(4)) : 0

    if (cost !== formData.cost_per_espresso) {
      setFormData(prev => ({ ...prev, cost_per_espresso: cost }))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.price, formData.discount_price, formData.coffee_type, formData.pack_quantity, formData.pack_weight_grams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const url = product 
        ? `/api/admin/products/${product.id}` 
        : '/api/admin/products'
      
      const method = product ? 'PATCH' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast({
          title: product ? t('admin.products.productForm.messages.productUpdated') : t('admin.products.productForm.messages.productCreated'),
          description: product
            ? t('admin.products.productForm.messages.updateSuccess')
            : t('admin.products.productForm.messages.createSuccess'),
        })
        router.push(`/${locale}/admin/products`)
      } else {
        const error = await response.json()
        throw new Error(error.error || t('admin.products.productForm.messages.saveError'))
      }
    } catch (error) {
      console.error('Error saving product:', error)
      toast({
        title: t('admin.products.productForm.messages.error'),
        description: error instanceof Error ? error.message : t('admin.products.productForm.messages.saveError'),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Template Selector */}
      <div className="mb-6">
        <ProductTemplateSelector
          selectedTemplate={selectedTemplate}
          onTemplateSelect={handleTemplateSelect}
          onTemplateApply={handleTemplateApply}
          category={formData.category as 'coffee' | 'accessories'}
          showPreview={true}
          previewData={formData}
        />
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="title">{t('admin.products.productForm.titleRequired')}</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
              disabled={useTemplate && !!formData.title_template}
            />
            {useTemplate && formData.title_template && (
              <p className="text-xs text-muted-foreground mt-1">
                {t('admin.products.templates.titleGeneratedFromTemplate')}
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="description">{t('admin.products.productForm.description')}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="category">{t('admin.products.productForm.categoryRequired')}</Label>
            <Select
              value={formData.category}
              onValueChange={(value) => setFormData({ ...formData, category: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="coffee">{t('admin.products.productForm.categories.coffee')}</SelectItem>
                <SelectItem value="accessories">{t('admin.products.productForm.categories.accessories')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {formData.category === 'coffee' && (
            <div>
              <Label htmlFor="coffee_type">{t('admin.products.productForm.coffeeType')}</Label>
              <Select
                value={formData.coffee_type}
                onValueChange={(value) => setFormData({ ...formData, coffee_type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('admin.products.productForm.selectType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="capsules">{t('admin.products.productForm.coffeeTypes.capsules')}</SelectItem>
                  <SelectItem value="pods">{t('admin.products.productForm.coffeeTypes.pods')}</SelectItem>
                  <SelectItem value="beans">{t('admin.products.productForm.coffeeTypes.beans')}</SelectItem>
                  <SelectItem value="ground">{t('admin.products.productForm.coffeeTypes.ground')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {formData.category === 'coffee' && formData.coffee_type === 'capsules' && (
            <div>
              <Label htmlFor="machine_compatibility">{t('admin.products.productForm.compatibleWith')}</Label>
              <Input
                id="machine_compatibility"
                value={formData.machine_compatibility.join(', ')}
                onChange={(e) => {
                  const compatibilityArray = e.target.value
                    .split(',')
                    .map(item => item.trim())
                    .filter(item => item.length > 0)
                  setFormData({ ...formData, machine_compatibility: compatibilityArray })
                }}
                placeholder={t('admin.products.productForm.compatibleWithPlaceholder')}
              />
              <p className="text-sm text-muted-foreground mt-1">
                {t('admin.products.productForm.compatibleWithHelp')}
              </p>
            </div>
          )}

          <div>
            <Label htmlFor="brand">{t('admin.products.productForm.brand')}</Label>
            <Input
              id="brand"
              value={formData.brand}
              onChange={(e) => setFormData({ ...formData, brand: e.target.value })}
            />
          </div>

          <div>
            <Label htmlFor="blend">{t('admin.products.productForm.blend')}</Label>
            <Input
              id="blend"
              value={formData.blend}
              onChange={(e) => setFormData({ ...formData, blend: e.target.value })}
            />
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="price">{t('admin.products.productForm.priceRequired')}</Label>
            <Input
              id="price"
              type="number"
              step="0.01"
              min="0"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
              required
            />
          </div>

          <div>
            <Label htmlFor="discount_price">{t('admin.products.productForm.discountPrice')}</Label>
            <Input
              id="discount_price"
              type="number"
              step="0.01"
              min="0"
              value={formData.discount_price}
              onChange={(e) => setFormData({ ...formData, discount_price: parseFloat(e.target.value) || 0 })}
            />
          </div>

          <div>
            <Label htmlFor="pack_quantity">{t('admin.products.productForm.packQuantity')}</Label>
            <Input
              id="pack_quantity"
              type="number"
              min="0"
              value={formData.pack_quantity}
              onChange={(e) => setFormData({ ...formData, pack_quantity: parseInt(e.target.value) || 0 })}
            />
          </div>

          <div>
            <Label htmlFor="pack_weight_grams">{t('admin.products.productForm.packWeight')}</Label>
            <Input
              id="pack_weight_grams"
              type="number"
              min="0"
              value={formData.pack_weight_grams}
              onChange={(e) => setFormData({ ...formData, pack_weight_grams: parseInt(e.target.value) || 0 })}
            />
          </div>

          <div>
            <Label htmlFor="cost_per_espresso">{t('admin.products.productForm.costPerEspresso')}</Label>
            <Input
              id="cost_per_espresso"
              type="number"
              step="0.01"
              min="0"
              value={formData.cost_per_espresso}
              readOnly
              disabled
            />
          </div>

          <div>
            <Label htmlFor="inventory_count">{t('admin.products.productForm.inventory')}</Label>
            <Input
              id="inventory_count"
              type="number"
              min="0"
              value={formData.inventory_count}
              onChange={(e) => setFormData({ ...formData, inventory_count: parseInt(e.target.value) || 0 })}
            />
          </div>

          <div>
            <Label htmlFor="purchase_cost">{t('admin.products.productForm.purchaseCost')}</Label>
            <Input
              id="purchase_cost"
              type="number"
              step="0.01"
              min="0"
              value={formData.purchase_cost}
              onChange={(e) => setFormData({ ...formData, purchase_cost: parseFloat(e.target.value) || 0 })}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_available"
              checked={formData.is_available}
              onCheckedChange={(checked) => setFormData({ ...formData, is_available: !!checked })}
            />
            <Label htmlFor="is_available">{t('admin.products.productForm.available')}</Label>
          </div>
        </div>
      </div>

      {/* Image Upload */}
      <ImageUpload
        images={formData.images}
        onImagesChange={(images) => setFormData({ ...formData, images })}
        folder="products"
        maxImages={5}
        label={t('admin.products.productForm.productImages')}
      />

      <div className="flex gap-4">
        <Button type="submit" disabled={loading}>
          {loading ? t('admin.products.productForm.saving') : (product ? t('admin.products.productForm.update') : t('admin.products.productForm.create'))}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push(`/${locale}/admin/products`)}
        >
          {t('admin.products.productForm.cancel')}
        </Button>
      </div>
    </form>
  )
}
