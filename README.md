# PrimeCaffe E-commerce App

A comprehensive e-commerce application for **PrimeCaffe** (www.primecaffe.ch) built with **Next.js 15**, **Supabase**, and **Stripe**.

## 🚀 Features

### Core Functionality
- **Traditional Shop** - Browse and purchase individual products
- **Pre-made Bundles** - Curated coffee combinations
- **Coffee Box Builder** - Interactive tool to create custom coffee boxes (Primary CTA)
- **Gamification System** - User levels, points, and rewards
- **Gift Thresholds** - Free products based on order value
- **Swiss Compliance** - GDPR/Swiss data protection compliant

### Technical Stack
- **Framework**: Next.js 15 (App Router)
- **Database & Auth**: Supabase
- **Payments**: Stripe
- **UI**: shadcn/ui + Tailwind CSS
- **Testing**: Jest
- **Deployment**: Vercel (Edge/Serverless optimized)

### User Experience
- **Timezone**: Europe/Zurich
- **Date Format**: dd/mm/yyyy
- **Currency**: CHF
- **Target Audience**: 40-50+ age group friendly
- **Design Philosophy**: Less is more

## 🛠️ Setup Instructions

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm
- Supabase account
- Stripe account

### 1. Clone and Install
```bash
git clone <repository-url>
cd primecaffe
npm install
```

### 2. Environment Variables
Copy `.env.example` to `.env.local` and fill in your values:

```bash
cp .env.example .env.local
```

Required environment variables:
- `NEXT_PUBLIC_SUPABASE_URL` - Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key
- `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` - Your Stripe publishable key
- `STRIPE_SECRET_KEY` - Your Stripe secret key
- `STRIPE_WEBHOOK_SECRET` - Your Stripe webhook secret
- `SMTP_*` variables for email service
- `NEXT_PUBLIC_GTM_ID` - Google Tag Manager ID (optional)
- `NEXT_PUBLIC_META_PIXEL_ID` - Meta Pixel ID (optional)

### 3. Database Setup
1. Create a new Supabase project
2. Run the schema setup:
```sql
-- Copy and run the contents of supabase/schema.sql
-- Copy and run the contents of supabase/policies.sql
-- Copy and run the contents of supabase/seed.sql (optional, for sample data)
```

### 4. Create Storage Bucket
Run the helper script to create the `product-images` bucket used for uploads:

```bash
node scripts/create-storage-bucket.js
```

This requires `SUPABASE_SERVICE_ROLE_KEY` to be set in `.env.local`.

### 5. Stripe Setup
1. Create a Stripe account
2. Get your API keys from the Stripe dashboard
3. Set up webhooks for order processing

### 6. Development
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── (auth)/            # Authentication pages
│   ├── (shop)/            # Shopping pages
│   ├── admin/             # Admin dashboard
│   └── api/               # API routes
├── components/            # Reusable UI components
│   ├── ui/                # shadcn/ui components
│   └── layout/            # Layout components
├── lib/                   # Utilities and configurations
│   ├── supabase/          # Supabase client setup
│   ├── stripe.ts          # Stripe configuration
│   ├── email.ts           # Email service
│   └── utils.ts           # Helper functions
├── types/                 # TypeScript type definitions
└── hooks/                 # Custom React hooks

supabase/
├── schema.sql             # Database schema
├── policies.sql           # Row Level Security policies
└── seed.sql              # Sample data
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run linting
npm run lint
```

## 🚀 Deployment

### Vercel Deployment
1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Variables for Production
Ensure all environment variables are set in your deployment platform:
- Database credentials
- Stripe keys
- SMTP configuration
- Analytics IDs (GTM, Meta Pixel)

## 📊 Key Features Detail

### Coffee Box Builder
- Primary CTA on homepage
- Interactive product selection
- Real-time price calculation
- Gift threshold tracking
- Pack-size multiple constraints

### Gamification System
- 5 user levels based on lifetime spend
- Points earning (1 CHF = 1 point base)
- Level-based discounts
- Multiplier bonuses

### Admin Dashboard
- Product CRUD operations
- Order management with tracking
- Customer management
- KPI dashboard with filters
- Coupon system
- Cart simulator

### Swiss Compliance
- Cookie banner with granular controls
- GDPR-compliant data handling
- Swiss-specific legal pages
- Proper consent management

## 🔧 Configuration

### Cost-per-Espresso Calculation
- **Capsules/Pods**: `price ÷ pack_quantity`
- **Beans/Ground**: `price ÷ (pack_weight_grams ÷ 7g)`
- Configurable grams per espresso (default: 7g)

### Shipping Configuration
- Country-specific rates
- Free shipping thresholds
- Automatic calculation

### Email Templates
- Order confirmation
- Shipping notifications
- Admin notifications
- Swiss German language

## 📝 License

This project is proprietary software for PrimeCaffe.

## 🤝 Contributing

This is a private project. For internal development guidelines, see the development documentation.

---

**Built with ❤️ in Switzerland for PrimeCaffe.ch**
